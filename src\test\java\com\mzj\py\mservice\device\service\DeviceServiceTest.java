package com.mzj.py.mservice.device.service;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.mzj.py.commons.RedisKeyConstant;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.exception.ServiceException;
import com.mzj.py.mservice.compound.entity.SpeechSynthesizerDto;
import com.mzj.py.mservice.device.vo.*;
import com.mzj.py.mservice.home.entity.*;
import com.mzj.py.mservice.home.repository.*;
import com.mzj.py.mservice.home.service.AnchorService;
import com.mzj.py.mservice.oss.service.OSSService;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.shop.entity.ShopUserRef;
import com.mzj.py.mservice.shop.repository.ShopRepository;
import com.mzj.py.mservice.shop.repository.ShopUserRefRepository;
import com.mzj.py.mservice.shop.service.StoreService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DeviceServiceTest {

    @InjectMocks
    private DeviceService deviceService;

    @Mock
    private JdbcTemplate jdbcTemplate;
    @Mock
    private DeviceRepository deviceRepository;
    @Mock
    private DeviceVoiceRepository deviceVoiceRepository;
    @Mock
    private OSSService ossService;
    @Mock
    private StoreService shopService;
    @Mock
    private ShopRepository shopRepository;
    @Mock
    private ShopUserRefRepository shopUserRefRepository;
    @Mock
    private RemoteDeviceService remoteDeviceService;
    @Mock
    private AnchorService anchorService;
    @Mock
    private DubAnchorRepository dubAnchorRepository;
    @Mock
    private RedisService redisService;
    @Mock
    private VoicePacketRepository voicePacketRepository;
    @Mock
    private VoiceWorkRepository workRepository;

    private Device mockDevice;
    private DeviceVoice mockDeviceVoice;
    private DeviceQueryVo mockQueryVo;
    private DeviceUnBindVo mockUnBindVo;
    private DeviceVoiceVo mockDeviceVoiceVo;
    private DeviceVoiceAddParam mockAddParam;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(deviceService, "cdnUrl", "https://test-oss.com/");

        mockDevice = new Device();
        mockDevice.setId(1L);
        mockDevice.setName("Test Device");
        mockDevice.setSn("TEST001");
        mockDevice.setUserId(1L);
        mockDevice.setShopId(1L);
        mockDevice.setVolume(50);
        mockDevice.setStatus(1);
        mockDevice.setBindStatus(1);
        mockDevice.setDelStatus(0);

        mockDeviceVoice = new DeviceVoice();
        mockDeviceVoice.setId(1L);
        mockDeviceVoice.setDeviceId(1L);
        mockDeviceVoice.setTitle("Test Voice");
        mockDeviceVoice.setVoiceUrl("test-voice.mp3");
        mockDeviceVoice.setSpeed(50);
        mockDeviceVoice.setVolume(50);
        mockDeviceVoice.setPitch(50);
        mockDeviceVoice.setBackgroundMusicVolume(30);

        mockQueryVo = new DeviceQueryVo();
        mockQueryVo.setPageSize(10);
        mockQueryVo.setPageNumber(1);
        mockQueryVo.setShopIds(Arrays.asList(1L, 2L));
        mockQueryVo.setCreateId(1L);

        mockUnBindVo = new DeviceUnBindVo();
        mockUnBindVo.setId(1L);
        mockUnBindVo.setShopId(1L);

        mockDeviceVoiceVo = new DeviceVoiceVo();
        mockDeviceVoiceVo.setId(1L);
        mockDeviceVoiceVo.setDeviceId(1L);
        mockDeviceVoiceVo.setSpeed(60);
        mockDeviceVoiceVo.setVolume(60);
        mockDeviceVoiceVo.setPitch(60);
        mockDeviceVoiceVo.setBackgroundMusicVolume(40);
        mockDeviceVoiceVo.setVoiceUrl("updated-voice.mp3");

        mockAddParam = new DeviceVoiceAddParam();
        mockAddParam.setDeviceIds(Arrays.asList(1L, 2L));
        mockAddParam.setTitle("New Voice");
        mockAddParam.setContent("Test content");
        mockAddParam.setVoiceId(1L);
        mockAddParam.setBackgroundMusicId(1L);
        mockAddParam.setBackgroundMusicVolume(30);
        mockAddParam.setShopId(1L);
        mockAddParam.setUserId(1L);
    }

    @Test
    @DisplayName("测试设备列表查询 - 成功场景")
    void testList_Success() {
        // Given
        List<Device> devices = Arrays.asList(mockDevice);
        Page<Device> page = new PageImpl<>(devices, mock(Pageable.class), 1);
        when(deviceRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        // When
        ResultBean<Map<String, Object>> result = deviceService.list(mockQueryVo);

        // Then
        assertTrue(result.isOk());
        Map<String, Object> data = result.getResultData();
        assertEquals(1, data.get("count"));
        assertEquals(devices, data.get("result"));
    }

    @Test
    @DisplayName("测试设备列表查询 - 门店ID列表为空")
    void testList_EmptyShopIds() {
        // Given
        mockQueryVo.setShopIds(Collections.emptyList());

        // When
        ResultBean<Map<String, Object>> result = deviceService.list(mockQueryVo);

        // Then
        assertTrue(result.isOk());
        Map<String, Object> data = result.getResultData();
        assertEquals(0, data.get("count"));
        assertTrue(((List<?>) data.get("result")).isEmpty());
    }

    @Test
    @DisplayName("测试设备列表查询 - 带关键字搜索")
    void testList_WithKeyword() {
        // Given
        mockQueryVo.setKeyword("Test");
        List<Device> devices = Arrays.asList(mockDevice);
        Page<Device> page = new PageImpl<>(devices, mock(Pageable.class), 1);
        when(deviceRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        // When
        ResultBean<Map<String, Object>> result = deviceService.list(mockQueryVo);

        // Then
        assertTrue(result.isOk());
        verify(deviceRepository).findAll(any(Specification.class), any(Pageable.class));
    }

    @Test
    @DisplayName("测试设备列表查询 - 带状态和绑定状态筛选")
    void testList_WithStatusAndBindStatus() {
        // Given
        mockQueryVo.setStatus(1);
        mockQueryVo.setBindStatus(1);
        List<Device> devices = Arrays.asList(mockDevice);
        Page<Device> page = new PageImpl<>(devices, mock(Pageable.class), 1);
        when(deviceRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        // When
        ResultBean<Map<String, Object>> result = deviceService.list(mockQueryVo);

        // Then
        assertTrue(result.isOk());
        verify(deviceRepository).findAll(any(Specification.class), any(Pageable.class));
    }

    @Test
    @DisplayName("测试设备解绑 - 成功场景")
    void testUnBind_Success() {
        // Given
        when(deviceRepository.findById(1L)).thenReturn(Optional.of(mockDevice));
        when(deviceRepository.save(any(Device.class))).thenReturn(mockDevice);

        // When
        ResultBean<Boolean> result = deviceService.unBind(1L);

        // Then
        assertTrue(result.isOk());
        assertTrue(result.getResultData());
        verify(deviceRepository).save(any(Device.class));
    }

    @Test
    @DisplayName("测试设备解绑 - 设备不存在")
    void testUnBind_DeviceNotFound() {
        // Given
        when(deviceRepository.findById(1L)).thenReturn(Optional.empty());

        // When
        ResultBean<Boolean> result = deviceService.unBind(1L);

        // Then
        assertFalse(result.isOk());
        assertEquals("设备不存在", result.getMsg());
    }

    @Test
    @DisplayName("测试设备绑定 - 成功场景")
    void testBind_Success() {
        // Given
        // Ensure the device is currently unbound (no shopId)
        mockDevice.setShopId(null);
        when(deviceRepository.existsById(1L)).thenReturn(true);
        when(shopRepository.existsById(1L)).thenReturn(true);

        ShopUserRef shopRef = new ShopUserRef();
        shopRef.setUserId(1L);
        when(shopUserRefRepository.findByShopIdAndRole(1L, 1)).thenReturn(shopRef);
        when(deviceRepository.findById(1L)).thenReturn(Optional.of(mockDevice));
        when(deviceRepository.save(any(Device.class))).thenReturn(mockDevice);

        // When
        ResultBean<Boolean> result = deviceService.bind(mockUnBindVo);

        // Then
        assertTrue(result.isOk());
        assertTrue(result.getResultData());
        verify(deviceRepository).save(any(Device.class));
    }

    @Test
    @DisplayName("测试设备绑定 - 设备不存在")
    void testBind_DeviceNotExists() {
        // Given
        when(deviceRepository.existsById(1L)).thenReturn(false);

        // When
        ResultBean<Boolean> result = deviceService.bind(mockUnBindVo);

        // Then
        assertFalse(result.isOk());
        assertEquals("设备不存在", result.getMsg());
    }

    @Test
    @DisplayName("测试设备绑定 - 门店不存在")
    void testBind_ShopNotExists() {
        // Given
        when(deviceRepository.existsById(1L)).thenReturn(true);
        when(shopRepository.existsById(1L)).thenReturn(false);

        // When
        ResultBean<Boolean> result = deviceService.bind(mockUnBindVo);

        // Then
        assertFalse(result.isOk());
        assertEquals("门店不存在", result.getMsg());
    }

    @Test
    @DisplayName("测试设备绑定 - 门店未绑定管理员")
    void testBind_NoShopAdmin() {
        // Given
        when(deviceRepository.existsById(1L)).thenReturn(true);
        when(shopRepository.existsById(1L)).thenReturn(true);
        when(shopUserRefRepository.findByShopIdAndRole(1L, 1)).thenReturn(null);

        // When
        ResultBean<Boolean> result = deviceService.bind(mockUnBindVo);

        // Then
        assertFalse(result.isOk());
        assertEquals("门店未绑定管理员", result.getMsg());
    }

    @Test
    @DisplayName("测试设备新增或更新 - 新增设备成功")
    void testAddOrUpdate_AddDevice_Success() {
        // Given
        Device newDevice = new Device();
        newDevice.setName("New Device");
        newDevice.setSn("NEW001");
        newDevice.setUserId(1L);
        newDevice.setVolume(50);

        ShopUserRef ref = new ShopUserRef();
        ref.setShopId(1L);
        ref.setUserId(1L);
        when(shopService.createShop(1L)).thenReturn(ref);
        when(deviceRepository.save(any(Device.class))).thenReturn(newDevice);

        // When
        ResultBean<Boolean> result = deviceService.addOrUpdate(newDevice);

        // Then
        assertTrue(result.isOk());
        assertTrue(result.getResultData());
        verify(deviceRepository).save(any(Device.class));
    }

    @Test
    @DisplayName("测试设备新增或更新 - 新增设备时创建门店失败")
    void testAddOrUpdate_AddDevice_CreateShopFailed() {
        // Given
        Device newDevice = new Device();
        newDevice.setName("New Device");
        newDevice.setUserId(1L);
        when(shopService.createShop(1L)).thenReturn(null);

        // When & Then
        assertThrows(ServiceException.class, () -> deviceService.addOrUpdate(newDevice));
    }

    @Test
    @DisplayName("测试设备新增或更新 - 更新设备成功")
    void testAddOrUpdate_UpdateDevice_Success() {
        // Given
        mockDevice.setShopId(1L);
        when(deviceRepository.existsById(1L)).thenReturn(true);
        when(deviceRepository.findById(1L)).thenReturn(Optional.of(mockDevice));
        when(deviceRepository.save(any(Device.class))).thenReturn(mockDevice);

        // When
        ResultBean<Boolean> result = deviceService.addOrUpdate(mockDevice);

        // Then
        assertTrue(result.isOk());
        assertTrue(result.getResultData());
        verify(deviceRepository).save(any(Device.class));
    }

    @Test
    @DisplayName("测试设备新增或更新 - 更新设备不存在")
    void testAddOrUpdate_UpdateDevice_NotExists() {
        // Given
        when(deviceRepository.existsById(1L)).thenReturn(false);

        // When & Then
        assertThrows(ServiceException.class, () -> deviceService.addOrUpdate(mockDevice));
    }

    @Test
    @DisplayName("测试设备新增或更新 - 更新设备非所有者")
    void testAddOrUpdate_UpdateDevice_NotOwner() {
        // Given
        Device existingDevice = new Device();
        existingDevice.setShopId(2L);
        existingDevice.setUserId(2L); // 不同的用户ID
        mockDevice.setShopId(1L);
        mockDevice.setUserId(1L); // 当前用户ID

        when(deviceRepository.existsById(1L)).thenReturn(true);
        when(deviceRepository.findById(1L)).thenReturn(Optional.of(existingDevice));

        // When
        ResultBean<Boolean> result = deviceService.addOrUpdate(mockDevice);

        // Then - 业务逻辑应该允许更新，因为没有权限验证逻辑
        assertTrue(result.isOk());
        verify(deviceRepository).save(any(Device.class));
    }

    @Test
    @DisplayName("测试设备详情 - 成功场景")
    void testDetail_Success() {
        // Given
        when(deviceRepository.findById(1L)).thenReturn(Optional.of(mockDevice));
        when(jdbcTemplate.queryForObject(contains("count(1)"), eq(Long.class), any())).thenReturn(1L);
        when(jdbcTemplate.query(contains("SELECT ddv.*"), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(Arrays.asList(mockDeviceVoice));

        // When
        ResultBean<DetailVo> result = deviceService.detail(1L, 10, 0);

        // Then
        assertTrue(result.isOk());
        assertNotNull(result.getResultData());
        assertNotNull(result.getResultData().getMap());
    }

    @Test
    @DisplayName("测试设备详情 - 设备ID为空")
    void testDetail_DeviceIdNull() {
        // When
        ResultBean<DetailVo> result = deviceService.detail(null, 10, 0);

        // Then
        assertFalse(result.isOk());
        assertEquals("设备id不能为空", result.getMsg());
    }

    @Test
    @DisplayName("测试设备详情 - 设备不存在")
    void testDetail_DeviceNotExists() {
        // Given
        when(deviceRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ServiceException.class, () -> deviceService.detail(1L, 10, 0));
    }

    @Test
    @DisplayName("测试设备详情 - 无语音数据")
    void testDetail_NoData() {
        // Given
        when(deviceRepository.findById(1L)).thenReturn(Optional.of(mockDevice));
        when(jdbcTemplate.queryForObject(contains("count(1)"), eq(Long.class), any())).thenReturn(0L);

        // When
        ResultBean<DetailVo> result = deviceService.detail(1L, 10, 0);

        // Then
        assertTrue(result.isOk());
        DetailVo detailVo = result.getResultData();
        assertNotNull(detailVo);
        Map<String, Object> map = detailVo.getMap();
        assertEquals(0, map.get("count"));
        assertNull(map.get("result"));
    }

    @Test
    @DisplayName("测试更新设备语音 - 成功场景")
    void testUpdateDeviceVoice_Success() {
        // Given
        when(deviceVoiceRepository.findById(1L)).thenReturn(Optional.of(mockDeviceVoice));
        when(deviceVoiceRepository.save(any(DeviceVoice.class))).thenReturn(mockDeviceVoice);

        // When
        ResultBean<Boolean> result = deviceService.updateDeviceVoice(mockDeviceVoiceVo);

        // Then
        assertTrue(result.isOk());
        verify(deviceVoiceRepository).save(any(DeviceVoice.class));
    }

    @Test
    @DisplayName("测试更新设备语音 - 参数校验失败")
    void testUpdateDeviceVoice_ValidationFailures() {
        // Test null ID
        DeviceVoiceVo nullIdVo = new DeviceVoiceVo();
        nullIdVo.setId(null);
        ResultBean<Boolean> result1 = deviceService.updateDeviceVoice(nullIdVo);
        assertFalse(result1.isOk());
        assertEquals("设备与语音表id不能为空", result1.getMsg());

        // Test null device ID
        DeviceVoiceVo nullDeviceIdVo = new DeviceVoiceVo();
        nullDeviceIdVo.setId(1L);
        nullDeviceIdVo.setDeviceId(null);
        ResultBean<Boolean> result2 = deviceService.updateDeviceVoice(nullDeviceIdVo);
        assertFalse(result2.isOk());
        assertEquals("设备id不能为空", result2.getMsg());

        // Test null speed
        DeviceVoiceVo nullSpeedVo = new DeviceVoiceVo();
        nullSpeedVo.setId(1L);
        nullSpeedVo.setDeviceId(1L);
        nullSpeedVo.setSpeed(null);
        ResultBean<Boolean> result3 = deviceService.updateDeviceVoice(nullSpeedVo);
        assertFalse(result3.isOk());
        assertEquals("语速不能为空", result3.getMsg());

        // Test null volume
        DeviceVoiceVo nullVolumeVo = new DeviceVoiceVo();
        nullVolumeVo.setId(1L);
        nullVolumeVo.setDeviceId(1L);
        nullVolumeVo.setSpeed(50);
        nullVolumeVo.setVolume(null);
        ResultBean<Boolean> result4 = deviceService.updateDeviceVoice(nullVolumeVo);
        assertFalse(result4.isOk());
        assertEquals("音量不能为空", result4.getMsg());

        // Test null background music volume
        DeviceVoiceVo nullBgVolumeVo = new DeviceVoiceVo();
        nullBgVolumeVo.setId(1L);
        nullBgVolumeVo.setDeviceId(1L);
        nullBgVolumeVo.setSpeed(50);
        nullBgVolumeVo.setVolume(50);
        nullBgVolumeVo.setPitch(50);
        nullBgVolumeVo.setBackgroundMusicId(1L);
        nullBgVolumeVo.setBackgroundMusicVolume(null);
        ResultBean<Boolean> result6 = deviceService.updateDeviceVoice(nullBgVolumeVo);
        assertFalse(result6.isOk());
        assertEquals("背景音乐音量不能为空", result6.getMsg());

        // Test blank voice URL
        DeviceVoiceVo blankUrlVo = new DeviceVoiceVo();
        blankUrlVo.setId(1L);
        blankUrlVo.setDeviceId(1L);
        blankUrlVo.setSpeed(50);
        blankUrlVo.setVolume(50);
        blankUrlVo.setPitch(50);
        blankUrlVo.setBackgroundMusicVolume(30);
        blankUrlVo.setVoiceUrl("");
        ResultBean<Boolean> result7 = deviceService.updateDeviceVoice(blankUrlVo);
        assertFalse(result7.isOk());
        assertEquals("语音包路径不能为空", result7.getMsg());
    }

    @Test
    @DisplayName("测试删除设备语音 - 成功场景")
    void testDeleteDeviceVoice_Success() {
        // Given
        when(deviceVoiceRepository.findById(1L)).thenReturn(Optional.of(mockDeviceVoice));
        when(deviceVoiceRepository.updateDelStatusById(1L)).thenReturn(1);

        // When
        ResultBean<Boolean> result = deviceService.deleteDeviceVoice(1L);

        // Then
        assertTrue(result.isOk());
        verify(deviceVoiceRepository).updateDelStatusById(1L);
    }

    @Test
    @DisplayName("测试删除设备语音 - ID为空")
    void testDeleteDeviceVoice_IdNull() {
        // When
        ResultBean<Boolean> result = deviceService.deleteDeviceVoice(null);

        // Then
        assertFalse(result.isOk());
        assertEquals("设备与语音包关系表id不能为空", result.getMsg());
    }

    @Test
    @DisplayName("测试删除设备语音 - 未找到记录")
    void testDeleteDeviceVoice_NotFound() {
        // Given
        when(deviceVoiceRepository.findById(1L)).thenReturn(Optional.empty());

        // When
        ResultBean<Boolean> result = deviceService.deleteDeviceVoice(1L);

        // Then
        assertTrue(result.isOk()); // Returns success even if not found
        verify(deviceVoiceRepository, never()).updateDelStatusById(anyLong());
    }

    @Test
    @DisplayName("测试设备语音详情 - 成功场景")
    void testDeviceVoicedetail_Success() {
        // Given
        Long deviceId = 1L;
        List<DevVoiceVo> voiceVoList = Arrays.asList(createDevVoiceVo("voice1.mp3"));

        when(deviceRepository.findById(deviceId)).thenReturn(Optional.of(mockDevice));
        when(jdbcTemplate.query(contains("SELECT ddv.*"), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(Arrays.asList(mockDeviceVoice));

        // When
        ResultBean<DetailVo> result = deviceService.deviceVoicedetail(deviceId, voiceVoList);

        // Then
        assertTrue(result.isOk());
        assertNotNull(result.getResultData());
    }

    @Test
    @DisplayName("测试新增设备语音 - 成功场景")
    void testAddDeviceVoice_Success() {
        // Given
        mockAddParam.setUrl("test-voice-url.mp3");
        mockAddParam.setTime(10);

        VoicePacket savedVoicePacket = new VoicePacket();
        savedVoicePacket.setId(1L);
        when(voicePacketRepository.save(any(VoicePacket.class))).thenReturn(savedVoicePacket);

        VoiceWork savedVoiceWork = new VoiceWork();
        savedVoiceWork.setId(1L);
        when(workRepository.save(any(VoiceWork.class))).thenReturn(savedVoiceWork);

        when(deviceVoiceRepository.saveAll(anyList())).thenReturn(Arrays.asList(mockDeviceVoice));

        // When
        ResultBean<Boolean> result = deviceService.addDeviceVoice(mockAddParam);

        // Then
        assertTrue(result.isOk());
        verify(deviceVoiceRepository).saveAll(anyList());
        verify(voicePacketRepository, times(2)).save(any(VoicePacket.class));
        verify(workRepository).save(any(VoiceWork.class));
        verify(remoteDeviceService, times(2)).sendAudio(anyLong(), anyString(), anyLong(),anyString()); // 修正：应该是2次，因为有2个设备
    }

    @Test
    @DisplayName("测试新增设备语音 - 指定URL")
    void testAddDeviceVoice_WithProvidedUrl() {
        // Given
        mockAddParam.setUrl("provided-voice-url.mp3");
        mockAddParam.setTime(10);

        VoicePacket savedVoicePacket = new VoicePacket();
        savedVoicePacket.setId(1L);
        when(voicePacketRepository.save(any(VoicePacket.class))).thenReturn(savedVoicePacket);

        VoiceWork savedVoiceWork = new VoiceWork();
        savedVoiceWork.setId(1L);
        when(workRepository.save(any(VoiceWork.class))).thenReturn(savedVoiceWork);

        when(deviceVoiceRepository.saveAll(anyList())).thenReturn(Arrays.asList(mockDeviceVoice));

        // When
        ResultBean<Boolean> result = deviceService.addDeviceVoice(mockAddParam);

        // Then
        assertTrue(result.isOk());
        verify(deviceVoiceRepository).saveAll(anyList());
        verify(voicePacketRepository, times(2)).save(any(VoicePacket.class));
        verify(workRepository).save(any(VoiceWork.class));
        verify(remoteDeviceService, times(2)).sendAudio(anyLong(), anyString(), anyLong(), anyString()); // 修正：应该是2次，因为有2个设备
    }

    @Test
    @DisplayName("测试获取设备树选择 - 成功场景")
    void testGetTreeUserDeviceSelect_Success() {
        // Given
        List<DeviceOptionVo> deviceOptions = Arrays.asList(
                createDeviceOptionVo(1L, 1L, "Shop 1", "Device 1"),
                createDeviceOptionVo(2L, 1L, "Shop 1", "Device 2"),
                createDeviceOptionVo(3L, 2L, "Shop 2", "Device 3"));

        when(jdbcTemplate.query(contains("SELECT dd.*"), any(Object[].class), any(BeanPropertyRowMapper.class)))
                .thenReturn(deviceOptions);

        // When
        ResultBean<List<DeviceTreeOptionVo>> result = deviceService.getTreeUserDeviceSelect(Arrays.asList(1L));

        // Then
        assertTrue(result.isOk());
        List<DeviceTreeOptionVo> resultData = result.getResultData();
        assertNotNull(resultData);

        // Should have 2 shop groups
        long shopCount = resultData.stream().filter(vo -> vo.getType() == 1).count();
        assertEquals(2, shopCount);

        // Should have 3 devices
        long deviceCount = resultData.stream().filter(vo -> vo.getType() == 1).flatMap(vo -> vo.getDevice().stream())
                .count();
        assertEquals(3, deviceCount);
    }

    @Test
    @DisplayName("测试获取设备树选择 - 空结果")
    void testGetTreeUserDeviceSelect_EmptyList() {
        // Given
        when(jdbcTemplate.query(contains("SELECT dd.*"), any(Object[].class), any(BeanPropertyRowMapper.class)))
                .thenReturn(Collections.emptyList());

        // When
        ResultBean<List<DeviceTreeOptionVo>> result = deviceService.getTreeUserDeviceSelect(Arrays.asList(1L));

        // Then
        assertTrue(result.isOk());
        List<DeviceTreeOptionVo> resultData = result.getResultData();
        assertNotNull(resultData);
        assertTrue(resultData.isEmpty());
    }

    @Test
    @DisplayName("测试私有方法 getDeviceVoices")
    void testGetDeviceVoices_Private() throws Exception {
        // Given
        List<DevVoiceVo> voiceVoList = Arrays.asList(
                createDevVoiceVo("voice1.mp3"),
                createDevVoiceVo("voice2.mp3"));

        List<DeviceVoice> existingVoices = Arrays.asList(
                createDeviceVoice(1L, "voice1.mp3"),
                createDeviceVoice(2L, "voice3.mp3"));

        // When - use reflection to call private method
        java.lang.reflect.Method method = DeviceService.class
                .getDeclaredMethod("getDeviceVoices", List.class, List.class);
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<DeviceVoice> result = (List<DeviceVoice>) method.invoke(deviceService, voiceVoList, existingVoices);

        // Then
        assertNotNull(result);
        // Should contain voices from both lists
        assertTrue(result.size() >= 1);
    }

    @Test
    @DisplayName("测试私有方法 getOldDataList")
    void testGetOldDataList_Private() throws Exception {
        // Given
        List<DevVoiceVo> voiceVoList = Arrays.asList(
                createDevVoiceVo("voice1.mp3"));

        List<DeviceVoice> existingVoices = Arrays.asList(
                createDeviceVoice(1L, "voice1"),
                createDeviceVoice(2L, "voice2"));

        java.lang.reflect.Method method = DeviceService.class
                .getDeclaredMethod("getOldDataList", List.class, List.class);
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<Long> result = (List<Long>) method.invoke(deviceService, voiceVoList, existingVoices);

        // Then
        assertEquals(1, result.size());
        assertEquals(Long.valueOf(2L), result.get(0)); // voice2 should be deleted
    }

    @Test
    @DisplayName("测试设备语音详情 - 设备存在")
    void testDeviceVoicedetail_DeviceExists() {
        // Given
        Long deviceId = 1L;
        List<DevVoiceVo> voiceVoList = Arrays.asList(
                createDevVoiceVo("voice1.mp3"),
                createDevVoiceVo("voice2.mp3"));
        when(deviceRepository.findById(deviceId)).thenReturn(Optional.of(mockDevice));

        List<DeviceVoice> existingVoices = Arrays.asList(
                createDeviceVoice(1L, "voice1.mp3"),
                createDeviceVoice(2L, "voice3.mp3"));

        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(existingVoices);

        when(deviceVoiceRepository.update(anyList())).thenReturn(1);

        // When
        ResultBean<DetailVo> result = deviceService.deviceVoicedetail(deviceId, voiceVoList);

        // Then
        assertTrue(result.isOk());
        DetailVo detailVo = result.getResultData();
        assertNotNull(detailVo);

        @SuppressWarnings("unchecked")
        List<DeviceVoice> resultList = (List<DeviceVoice>) detailVo.getMap().get("result");
        assertTrue(resultList.size() >= 1);

        verify(deviceVoiceRepository, atLeastOnce()).update(anyList());
    }

    @Test
    @DisplayName("测试设备语音详情 - 设备不存在")
    void testDeviceVoicedetail_DeviceNotExists() {
        // Given
        Long deviceId = 1L;
        List<DevVoiceVo> voiceVoList = Arrays.asList(createDevVoiceVo("voice1.mp3"));

        // When & Then
        assertThrows(ServiceException.class, () -> deviceService.deviceVoicedetail(deviceId, voiceVoList));
    }

    @Test
    @DisplayName("测试更新设备语音 - OSS操作")
    void testUpdateDeviceVoice_OSSOperations() {
        // Given
        File mockFile = new File("test.mp3");
        when(deviceVoiceRepository.findById(1L)).thenReturn(Optional.of(mockDeviceVoice));
        when(ossService.getObjectFile(null, "updated-voice.mp3")).thenReturn(mockFile);
        when(ossService.putFile(null, mockFile, "voice")).thenReturn("new-voice-url.mp3");

        // When
        ResultBean<Boolean> result = deviceService.updateDeviceVoice(mockDeviceVoiceVo);

        // Then
        assertTrue(result.isOk());
        verify(ossService).getObjectFile(null, "updated-voice.mp3");
        verify(ossService).deleteObject(null, "updated-voice.mp3");
        verify(ossService).putFile(null, mockFile, "voice");
        verify(deviceVoiceRepository).save(argThat(dv -> "new-voice-url.mp3".equals(dv.getVoiceUrl())));
    }

    @Test
    @DisplayName("测试更新设备语音 - 记录未找到")
    void testUpdateDeviceVoice_DeviceVoiceNotFound() {
        // Given
        when(deviceVoiceRepository.findById(1L)).thenReturn(Optional.empty());

        // When
        ResultBean<Boolean> result = deviceService.updateDeviceVoice(mockDeviceVoiceVo);

        // Then
        assertTrue(result.isOk()); // Service still returns success even if not found
        verify(deviceVoiceRepository, never()).save(any());
    }

    @Test
    @DisplayName("测试删除设备语音 - 记录存在")
    void testDeleteDeviceVoice_DeviceVoiceExists() {
        // Given
        when(deviceVoiceRepository.findById(1L)).thenReturn(Optional.of(mockDeviceVoice));
        when(deviceVoiceRepository.updateDelStatusById(1L)).thenReturn(1);

        // When
        ResultBean<Boolean> result = deviceService.deleteDeviceVoice(1L);

        // Then
        assertTrue(result.isOk());
        verify(deviceVoiceRepository).updateDelStatusById(1L);
    }

    @Test
    @DisplayName("测试删除设备语音 - 记录不存在")
    void testDeleteDeviceVoice_DeviceVoiceNotExists() {
        // Given
        when(deviceVoiceRepository.findById(1L)).thenReturn(Optional.empty());

        // When
        ResultBean<Boolean> result = deviceService.deleteDeviceVoice(1L);

        // Then
        assertTrue(result.isOk()); // Still returns success
        verify(deviceVoiceRepository, never()).updateDelStatusById(anyLong());
    }

    @Test
    @DisplayName("测试设备详情 - 分页查询")
    void testDetail_WithPagination() {
        // Given
        Long deviceId = 1L;
        when(deviceRepository.findById(deviceId)).thenReturn(Optional.of(mockDevice));
        when(deviceRepository.findById(deviceId)).thenReturn(Optional.of(mockDevice));

        // Mock the count query separately from the data query
        when(jdbcTemplate.queryForObject(contains("count(1)"), eq(Long.class), any())).thenReturn(5L);

        List<DeviceVoice> voices = Arrays.asList(
                createDeviceVoice(1L, "voice1"),
                createDeviceVoice(2L, "voice2"));
        when(jdbcTemplate.query(contains("SELECT ddv.*"), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(voices);

        // When
        ResultBean<DetailVo> result = deviceService.detail(deviceId, 2, 0);

        // Then
        assertTrue(result.isOk());
        DetailVo detailVo = result.getResultData();
        Map<String, Object> map = detailVo.getMap();
        assertEquals(5L, map.get("count"));
        assertEquals(voices, map.get("result"));
    }

    @Test
    @DisplayName("测试设备详情 - 未找到语音")
    void testDetail_NoVoicesFound() {
        // Given
        Long deviceId = 1L;
        when(deviceRepository.findById(deviceId)).thenReturn(Optional.of(mockDevice));
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any())).thenReturn(0L);

        // When
        ResultBean<DetailVo> result = deviceService.detail(deviceId, 10, 0);

        // Then
        assertTrue(result.isOk());
        DetailVo detailVo = result.getResultData();
        Map<String, Object> map = detailVo.getMap();
        assertEquals(0, map.get("count"));
        assertNull(map.get("result"));
    }

    @Test
    @DisplayName("测试设备绑定 - 已绑定其他门店")
    void testBind_DeviceAlreadyBound() {
        // Given
        mockDevice.setShopId(2L); // Already bound to another shop
        when(deviceRepository.existsById(1L)).thenReturn(true);
        when(shopRepository.existsById(1L)).thenReturn(true);
        when(deviceRepository.findById(1L)).thenReturn(Optional.of(mockDevice));

        ShopUserRef shopRef = new ShopUserRef();
        shopRef.setUserId(1L);
        when(shopUserRefRepository.findByShopIdAndRole(1L, 1)).thenReturn(shopRef);

        // When
        ResultBean<Boolean> result = deviceService.bind(mockUnBindVo);

        // Then
        assertFalse(result.isOk());
        assertEquals("设备已绑定门店,请联系客服解绑", result.getMsg());
    }

    @Test
    @DisplayName("测试设备绑定 - 已绑定当前门店")
    void testBind_DeviceAlreadyBound_SameShop() {
        // Given
        mockDevice.setShopId(1L); // Already bound to the same shop
        when(deviceRepository.existsById(1L)).thenReturn(true);
        when(shopRepository.existsById(1L)).thenReturn(true);
        when(deviceRepository.findById(1L)).thenReturn(Optional.of(mockDevice));

        ShopUserRef shopRef = new ShopUserRef();
        shopRef.setUserId(1L);
        when(shopUserRefRepository.findByShopIdAndRole(1L, 1)).thenReturn(shopRef);

        // When
        ResultBean<Boolean> result = deviceService.bind(mockUnBindVo);

        // Then
        assertFalse(result.isOk());
        assertEquals("设备已绑定门店,请联系客服解绑", result.getMsg());
    }

    @Test
    @DisplayName("测试新增或更新 - 新增设备时已有门店")
    void testAddOrUpdate_AddDeviceWithExistingShop() {
        // Given
        Device newDevice = new Device();
        newDevice.setName("New Device");
        newDevice.setSn("NEW001");
        newDevice.setUserId(1L);
        newDevice.setShopId(1L); // Already has shop ID
        newDevice.setVolume(50);

        when(deviceRepository.save(any(Device.class))).thenReturn(newDevice);

        // When
        ResultBean<Boolean> result = deviceService.addOrUpdate(newDevice);

        // Then
        assertTrue(result.isOk());
        assertTrue(result.getResultData());
        verify(deviceRepository).save(any(Device.class));
        verify(shopService, never()).createShop(anyLong()); // Should not create shop
    }

    @Test
    @DisplayName("测试新增设备语音 - URL为空")
    void testAddDeviceVoice_EmptyUrl() {
        // Given
        mockAddParam.setUrl("");

        // When
        ResultBean<Boolean> result = deviceService.addDeviceVoice(mockAddParam);

        // Then
        assertFalse(result.isOk());
        assertEquals("请重新合成", result.getMsg());
    }

    @Test
    @DisplayName("测试新增设备语音 - URL为空值")
    void testAddDeviceVoice_NullUrl() {
        // Given
        mockAddParam.setUrl(null);

        // When
        ResultBean<Boolean> result = deviceService.addDeviceVoice(mockAddParam);

        // Then
        assertFalse(result.isOk());
        assertEquals("请重新合成", result.getMsg());
    }

    @Test
    @DisplayName("测试新增设备语音 - 推送远程音频")
    void testAddDeviceVoice_WithRemoteDeviceService() {
        // Given
        mockAddParam.setUrl("test-voice-url.mp3");
        mockAddParam.setTime(10);

        VoicePacket savedVoicePacket = new VoicePacket();
        savedVoicePacket.setId(1L);
        when(voicePacketRepository.save(any(VoicePacket.class))).thenReturn(savedVoicePacket);

        VoiceWork savedVoiceWork = new VoiceWork();
        savedVoiceWork.setId(1L);
        when(workRepository.save(any(VoiceWork.class))).thenReturn(savedVoiceWork);

        when(deviceVoiceRepository.saveAll(anyList())).thenReturn(Arrays.asList(mockDeviceVoice));

        // When
        ResultBean<Boolean> result = deviceService.addDeviceVoice(mockAddParam);

        // Then
        assertTrue(result.isOk());
        verify(remoteDeviceService, times(2)).sendAudio(anyLong(), anyString(), anyLong(),anyString());
        verify(voicePacketRepository, times(2)).save(any(VoicePacket.class)); // Called twice: initial save and update
        verify(workRepository).save(any(VoiceWork.class));
        verify(deviceVoiceRepository).saveAll(anyList());
    }

    @Test
    @DisplayName("测试获取设备树选择 - shopId为空")
    void testGetTreeUserDeviceSelect_WithNullShopId() {
        // Given
        List<DeviceOptionVo> deviceOptions = Arrays.asList(
                createDeviceOptionVoWithNullShop(1L, "Device 1"));

        when(jdbcTemplate.query(anyString(), any(Object[].class), any(BeanPropertyRowMapper.class)))
                .thenReturn(deviceOptions);

        // When
        ResultBean<List<DeviceTreeOptionVo>> result = deviceService.getTreeUserDeviceSelect(Arrays.asList(1L));

        // Then
        assertTrue(result.isOk());
        List<DeviceTreeOptionVo> resultData = result.getResultData();
        assertNotNull(resultData);
        assertTrue(resultData.isEmpty()); // Should filter out devices with null shopId
    }

    @Test
    @DisplayName("测试设备语音详情 - 删除旧数据")
    void testDeviceVoicedetail_WithOldDataDeletion() {
        // Given
        Long deviceId = 1L;
        List<DevVoiceVo> voiceVoList = Arrays.asList(
                createDevVoiceVo("voice1.mp3"));

        when(deviceRepository.findById(deviceId)).thenReturn(Optional.of(mockDevice));
        when(deviceRepository.findById(deviceId)).thenReturn(Optional.of(mockDevice));

        List<DeviceVoice> existingVoices = Arrays.asList(
                createDeviceVoice(1L, "voice1"),
                createDeviceVoice(2L, "voice2")); // This should be deleted

        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(existingVoices);

        when(deviceVoiceRepository.update(anyList())).thenReturn(1);

        // When
        ResultBean<DetailVo> result = deviceService.deviceVoicedetail(deviceId, voiceVoList);

        // Then
        assertTrue(result.isOk());
        verify(deviceVoiceRepository).update(argThat(list -> list.contains(2L)));
    }

    // Helper methods
    private DevVoiceVo createDevVoiceVo(String name) {
        DevVoiceVo vo = new DevVoiceVo();
        vo.setName(name);
        return vo;
    }

    private DeviceOptionVo createDeviceOptionVo(Long id, Long shopId, String shopName, String deviceName) {
        DeviceOptionVo vo = new DeviceOptionVo();
        vo.setId(id);
        vo.setShopId(shopId);
        vo.setShopName(shopName);
        vo.setName(deviceName);
        vo.setType(1);
        return vo;
    }

    private DeviceOptionVo createDeviceOptionVoWithNullShop(Long id, String deviceName) {
        DeviceOptionVo vo = new DeviceOptionVo();
        vo.setId(id);
        vo.setShopId(null); // Null shop ID
        vo.setName(deviceName);
        vo.setType(1);
        return vo;
    }

    private DeviceVoice createDeviceVoice(Long id, String title) {
        DeviceVoice voice = new DeviceVoice();
        voice.setId(id);
        voice.setTitle(title);
        return voice;
    }
}