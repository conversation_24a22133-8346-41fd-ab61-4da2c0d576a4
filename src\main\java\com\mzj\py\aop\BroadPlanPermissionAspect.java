package com.mzj.py.aop;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.enums.StoreUserTypeEnum;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.mservice.broadcastPlan.entity.BroadcastPlan;
import com.mzj.py.mservice.broadcastPlan.repository.BroadcastPlanRepository;
import com.mzj.py.mservice.common.ApiBaseController;
import com.mzj.py.mservice.home.entity.Device;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.shop.repository.ShopUserRefRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 基于 {@link StorePermission} 的门店访问控制切面。
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class BroadPlanPermissionAspect extends ApiBaseController {

    @Resource
    private RedisService redisService;
    @Resource
    private ShopUserRefRepository shopUserRefRepository;
    @Resource
    private BroadcastPlanRepository broadcastPlanRepository;

    @Pointcut("@annotation(com.mzj.py.aop.BroadPlanPermission)")
    public void permissionPointCut() {
    }

    @Around("permissionPointCut() && @annotation(broadPlanPermission)")
    public Object around(ProceedingJoinPoint joinPoint, BroadPlanPermission broadPlanPermission) throws Throwable {
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attrs == null) {
            return joinPoint.proceed();
        }
        HttpServletRequest request = attrs.getRequest();
        String accessToken = request.getHeader("accessToken");
        if (StringUtils.isBlank(accessToken)) {
            log.warn("accessToken missing in request, uri={}", request.getRequestURI());
            return ResultBean.failedResultOfToken();
        }
        TokenRedisVo userVo = redisService.findTokenVo(accessToken);
        if (userVo == null) {
            log.warn("token invalid or expired, token={}", accessToken);
            return ResultBean.failedResultOfToken();
        }
        Long userId = userVo.getId();
        Set<Long> planIds = new HashSet<>();
        List<Long> allowShopIds = getShopIds(accessToken);
        MethodSignature methodSig = (MethodSignature) joinPoint.getSignature();
        String[] paramNames = methodSig.getParameterNames();
        Object[] args = joinPoint.getArgs();
        String idParam = broadPlanPermission.key();
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            if (arg == null) {
                continue;
            }

            String paramName = (paramNames != null && i < paramNames.length) ? paramNames[i] : "";

            boolean paramExplicitMatch = StringUtils.isNotBlank(idParam) && idParam.equals(paramName);
            if (!paramExplicitMatch) {
                continue;
            }

            // 2. 直接是 Long 主键
            if (arg instanceof Long) {
                planIds.add((Long) arg);
                continue;
            }

            // 可能是 List<Long>
            if (arg instanceof Collection) {
                for (Object o : (Collection<?>) arg) {
                    if (o instanceof Long) {
                        planIds.add((Long) o);
                    }
                }
            }
        }
        if (planIds.isEmpty()) {
            return joinPoint.proceed(); // 无主键可校验，直接放行
        }
        for (Long planId : planIds) {
            Object result = checkDeviceAccess(joinPoint, planId, allowShopIds);
            if (result instanceof ResultBean) {
                return result;
            }
        }
        // 微信端用户（type = 2）使用 siteId 作为用户标识，通过 ShopUserRef 判断其在允许门店中的角色。
        boolean isAdmin = false;
        if (userId != null) {
            isAdmin = shopUserRefRepository.existsByUserIdAndRoleAndShopIdIn(
                    userId,
                    StoreUserTypeEnum.ADMIN.getCode(),
                    allowShopIds);
        }
        // 非管理员做写操作直接拒绝
        if (broadPlanPermission.operate() && !isAdmin) {
            throw new CustomException("非门店管理员，无权执行该操作");
        }
        return joinPoint.proceed();
    }
    public Object checkDeviceAccess(ProceedingJoinPoint joinPoint, Long deviceId,List<Long> allowShopIds) throws Throwable {
        BroadcastPlan plan = broadcastPlanRepository.findById(deviceId).orElse(null);
        if (plan == null) {
            return ResultBean.failedResultWithMsg("播报计划不存在");
        }

        Long storeId = plan.getShopId();
        if (storeId == null) {
            return joinPoint.proceed();
        }
        if (!hasStoreAccess(allowShopIds, storeId)) {
            return ResultBean.failedResultWithMsg("用户未绑定该门店或其下属门店");
        }
        return null;
    }
    /**
     * 判断用户是否绑定了 storeId 店铺或其父店铺。
     */
    private boolean hasStoreAccess(List<Long> allowShopIds, Long storeId) {
        // 若访问的是分店且用户绑定的是总店
        return allowShopIds.contains(storeId);
    }
}