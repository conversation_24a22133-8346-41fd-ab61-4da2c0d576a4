package com.mzj.py.mservice.device.controller;

import com.alibaba.fastjson.JSON;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.exception.ServiceException;
import com.mzj.py.mservice.device.service.DeviceService;
import com.mzj.py.mservice.device.vo.*;
import com.mzj.py.mservice.home.entity.Device;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.util.NestedServletException;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * DeviceController异常处理测试类
 * 专门测试各种异常情况和边界条件
 */
@ExtendWith(MockitoExtension.class)
class DeviceControllerExceptionTest {

    @InjectMocks
    private DeviceController deviceController;

    @Mock
    private DeviceService deviceService;

    private MockMvc mockMvc;
    private static final String ACCESS_TOKEN = "test-access-token";
    private static final String ACCESS_TOKEN_HEADER = "accessToken";

    @BeforeEach
    void setUp() {
        deviceController = spy(new DeviceController());
        try {
            java.lang.reflect.Field field = DeviceController.class.getDeclaredField("deviceService");
            field.setAccessible(true);
            field.set(deviceController, deviceService);
        } catch (Exception e) {
            // fallback
        }

        mockMvc = MockMvcBuilders.standaloneSetup(deviceController).build();

        // Mock ApiBaseController methods - use lenient to avoid unnecessary stubbing
        // exceptions
        TokenRedisVo mockUser = new TokenRedisVo();
        mockUser.setId(1L);
        lenient().doReturn(Arrays.asList(1L, 2L)).when(deviceController).getShopIds(ACCESS_TOKEN);
        lenient().doReturn(mockUser).when(deviceController).getUser(ACCESS_TOKEN);
        lenient().doReturn(1L).when(deviceController).getShopId(ACCESS_TOKEN);
    }

    @Test
    void testList_ServiceThrowsException() throws Exception {
        // Given
        DeviceQueryVo queryVo = new DeviceQueryVo();
        queryVo.setPageSize(10);
        queryVo.setPageNumber(1);

        when(deviceService.list(any(DeviceQueryVo.class)))
                .thenThrow(new RuntimeException("Database connection failed"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/device/list")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(queryVo)));
        });
    }

    @Test
    void testUnBind_ServiceThrowsServiceException() throws Exception {
        // Given
        when(deviceService.unBind(1L))
                .thenThrow(new ServiceException("设备操作失败"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(get("/mini/device/unBind/1")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN));
        });
    }

    @Test
    void testBindShop_ServiceThrowsRuntimeException() throws Exception {
        // Given
        DeviceUnBindVo bindVo = new DeviceUnBindVo();
        bindVo.setId(1L);
        bindVo.setShopId(1L);

        when(deviceService.bind(any(DeviceUnBindVo.class)))
                .thenThrow(new RuntimeException("权限不足"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/device/bindShop")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(bindVo)));
        });
    }

    @Test
    void testAdd_ServiceThrowsTransactionException() throws Exception {
        // Given
        DeviceAddVo addVo = new DeviceAddVo();
        addVo.setName("Test Device");
        addVo.setSn("TEST001");

        lenient().when(deviceService.addOrUpdate(any(Device.class)))
                .thenThrow(new RuntimeException("Transaction rollback"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/device/add")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(addVo)));
        });
    }

    @Test
    void testUpdate_ServiceThrowsNullPointerException() throws Exception {
        // Given
        DeviceUpdateVo updateVo = new DeviceUpdateVo();
        updateVo.setId(1L);
        updateVo.setName("Updated Device");

        lenient().when(deviceService.addOrUpdate(any(Device.class)))
                .thenThrow(new NullPointerException("Device not found"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/device/update")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(updateVo)));
        });
    }

    @Test
    void testDetail_ServiceThrowsIllegalArgumentException() throws Exception {
        // Given
        when(deviceService.detail(1L, 10, 0))
                .thenThrow(new IllegalArgumentException("Invalid device ID"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(get("/mini/device/detail")
                    .param("deviceId", "1")
                    .param("pageSize", "10")
                    .param("pageNumber", "0"));
        });
    }

    @Test
    void testDetailWithVoiceList_ServiceThrowsException() throws Exception {
        // Given
        List<DevVoiceVo> voiceList = Arrays.asList(new DevVoiceVo());

        when(deviceService.deviceVoicedetail(eq(1L), anyList()))
                .thenThrow(new RuntimeException("Voice processing failed"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/device/detail/1")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(voiceList)));
        });
    }

    @Test
    void testUpdateDeviceVoice_ServiceThrowsException() throws Exception {
        // Given
        DeviceVoiceVo voiceVo = new DeviceVoiceVo();
        voiceVo.setId(1L);
        voiceVo.setDeviceId(1L);

        when(deviceService.updateDeviceVoice(any(DeviceVoiceVo.class)))
                .thenThrow(new RuntimeException("Voice update failed"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/device/devicevoice")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(voiceVo)));
        });
    }

    @Test
    void testDeleteDeviceVoice_ServiceThrowsException() throws Exception {
        // Given
        when(deviceService.deleteDeviceVoice(1L))
                .thenThrow(new RuntimeException("Delete operation failed"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(delete("/mini/device")
                    .param("id", "1"));
        });
    }

    @Test
    void testGetTreeUserDeviceSelect_ServiceThrowsException() throws Exception {
        // Given
        lenient().when(deviceService.getTreeUserDeviceSelect(anyList()))
                .thenThrow(new RuntimeException("Tree query failed"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(get("/mini/device/tree/my")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN));
        });
    }

    @Test
    void testInvalidRequestBody_MalformedJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/mini/device/list")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                .contentType(MediaType.APPLICATION_JSON)
                .content("{invalid json"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testInvalidRequestBody_EmptyJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/mini/device/list")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                .contentType(MediaType.APPLICATION_JSON)
                .content(""))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testInvalidRequestBody_NullJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/mini/device/list")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testInvalidPathVariable_NonNumeric() throws Exception {
        // When & Then
        mockMvc.perform(get("/mini/device/unBind/abc")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testInvalidPathVariable_Negative() throws Exception {
        // Given
        when(deviceService.unBind(-1L))
                .thenReturn(ResultBean.failedResultWithMsg("无效的设备ID"));

        // When & Then
        mockMvc.perform(get("/mini/device/unBind/-1")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(false))
                .andExpect(jsonPath("$.msg").value("无效的设备ID"));
    }

    @Test
    void testInvalidRequestParameter_NonNumeric() throws Exception {
        // When & Then
        mockMvc.perform(get("/mini/device/detail")
                .param("deviceId", "abc")
                .param("pageSize", "10")
                .param("pageNumber", "0"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testInvalidRequestParameter_NegativePageSize() throws Exception {
        // Given
        when(deviceService.detail(1L, -10, 0))
                .thenReturn(ResultBean.failedResultWithMsg("页面大小不能为负数"));

        // When & Then
        mockMvc.perform(get("/mini/device/detail")
                .param("deviceId", "1")
                .param("pageSize", "-10")
                .param("pageNumber", "0"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(false))
                .andExpect(jsonPath("$.msg").value("页面大小不能为负数"));
    }

    @Test
    void testMissingRequiredParameter() throws Exception {
        // When & Then
        mockMvc.perform(delete("/mini/device"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testUnsupportedMediaType() throws Exception {
        // When & Then
        mockMvc.perform(post("/mini/device/list")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                .contentType(MediaType.TEXT_PLAIN)
                .content("plain text"))
                .andExpect(status().isUnsupportedMediaType());
    }

    @Test
    void testUnsupportedHttpMethod() throws Exception {
        // When & Then
        mockMvc.perform(put("/mini/device/list")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN))
                .andExpect(status().isMethodNotAllowed());
    }
}
