package com.mzj.py.mservice.deviceOperationLog.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2025/4/21
 * @description:
 */
@Data
@Entity
@Table(name = "dub_device_operation_log")
public class DeviceOperationLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 设备ID
    @Column(name = "device_id")
    private Long deviceId;

    // 操作用户ID
    @Column(name = "user_id")
    private Long userId;

    // 设备ID
    @Column(name = "shop_id")
    private Long shopId;

    // 类型
    @Column(name = "type")
    private String type;

    // 保存发送包的全部数据
    @Column(columnDefinition = "text", name = "content")
    private String content;

    // 发送时间
    @Column(name = "send_time")
    private Date sendTime;

    // 响应时间
    @Column(name = "response_time")
    private Date responseTime;

    // 响应内容
    @Column(name = "response_content")
    private String responseContent;

    // 状态
    @Column(name = "status")
    private Integer status;


}
