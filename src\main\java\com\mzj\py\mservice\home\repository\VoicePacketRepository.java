package com.mzj.py.mservice.home.repository;

import com.mzj.py.mservice.home.entity.VoicePacket;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.transaction.annotation.Transactional;

public interface VoicePacketRepository  extends JpaRepository<VoicePacket,Long>, JpaSpecificationExecutor<VoicePacket> {
    VoicePacket findByVoiceWorkId(Long id);

    @Transactional
    @Modifying
    void deleteByVoiceWorkId(Long id);
}
