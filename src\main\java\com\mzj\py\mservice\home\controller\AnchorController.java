package com.mzj.py.mservice.home.controller;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.mservice.common.ApiBaseController;
import com.mzj.py.mservice.compound.entity.SpeechSynthesizerDto;
import com.mzj.py.mservice.device.service.DeviceService;
import com.mzj.py.mservice.device.vo.DeviceVoiceAddParam;
import com.mzj.py.mservice.home.controller.request.RecordUploadReq;
import com.mzj.py.mservice.home.entity.*;
import com.mzj.py.mservice.home.service.AnchorService;
import com.mzj.py.mservice.home.vo.*;
import com.mzj.py.mservice.oss.service.OSSService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 主播
 */
@RestController
@RequestMapping("/home/<USER>")
public class AnchorController extends ApiBaseController {
    @Resource
    private AnchorService anchorService;

    @Resource
    private OSSService ossService;

    @Autowired
    private DeviceService deviceService;

    /**
     * 获取主播
     */
    @GetMapping("list")
    public ResultBean<List<AnchorVo>> list(@RequestParam(name = "id", required = false) Long id) {
        return anchorService.list(id);
    }

    /**
     * 获取主播
     */
    @GetMapping("longList")
    public ResultBean<List<AnchorVo>> longList(@RequestParam(name = "id", required = false) Long id) {
        return anchorService.longList(id);
    }

    /**
     * 查询所有主播分类
     */
    @GetMapping("type")
    public ResultBean<List<Map<String, String>>> typeList() {
        return anchorService.typeList();
    }

    /**
     * 通过分类名称查询主播
     */
    @GetMapping("/by/name")
    public ResultBean<Map<String, Object>> anchorByTypeName(@RequestHeader String accessToken,
            @RequestParam(name = "name") String name,
            @RequestParam(name = "pageNumber", defaultValue = "0") Integer pageNumber,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        return anchorService.anchorByTypeName(accessToken, name, pageNumber, pageSize);
    }

    /**
     * 查询所有主播分类
     */
    @GetMapping("longAchor")
    public ResultBean<Map<String, Object>> longAchor(@RequestHeader String accessToken,
            @RequestParam(name = "name") String name,
            @RequestParam(name = "pageNumber", defaultValue = "0") Integer pageNumber,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        return anchorService.longAchor(accessToken, name, pageNumber, pageSize);
    }

    /**
     * 查询模板分类
     */
    @GetMapping("templateType")
    public ResultBean<List<DubTemplateType>> templateType() {
        return anchorService.templateType();
    }

    /**
     * 根据类型查询模板
     */
    @GetMapping("/template")
    public ResultBean<Map<String, Object>> templateByType(@RequestParam(name = "id") Long id,
            @RequestParam(name = "pageNumber", defaultValue = "0") Integer pageNumber,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        return anchorService.templateByType(id, pageNumber, pageSize);
    }

    /**
     * 查询背景音乐类型
     */
    @GetMapping("/music/type")
    public ResultBean<List<DubBackgroundMusicType>> musicType() {
        return anchorService.musicType();
    }

    /**
     * 根据类型查询背景音乐
     */
    @GetMapping("music")
    public ResultBean<Map<String, Object>> musicList(@RequestHeader String accessToken,
            @RequestParam(name = "id") Long id,
            @RequestParam(name = "pageNumber", defaultValue = "0") Integer pageNumber,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        return anchorService.musicList(accessToken, id, pageNumber, pageSize);
    }

    /**
     * 获取我的背景音乐
     */
    @GetMapping("myMusic")
    public ResultBean<Map<String, Object>> myMusic(@RequestHeader String accessToken) {
        return anchorService.myMusic(accessToken);
    }

    /**
     * 删除我的背景音乐
     */
    @GetMapping("delMyMusic/{musicId}")
    public ResultBean<Object> delMyMusic(@RequestHeader String accessToken, @PathVariable("musicId") Long musicId) {
        return anchorService.delMyMusic(accessToken, musicId);
    }

    /**
     * 点击试听
     */
    @PostMapping("audition")
    private ResultBean<Map<String, Object>> audition(@RequestHeader String accessToken,
            @RequestBody SpeechSynthesizerDto dto) {
        TokenRedisVo vo = getUser(accessToken);
        dto.setUserId(vo.getId());
        return anchorService.audition(dto);
    }

    /**
     * 长语音合成
     */
    @PostMapping("longCompound")
    private ResultBean<Map<String, Object>> longCompound(@RequestBody SpeechSynthesizerDto dto,
            @RequestHeader String accessToken) {
        TokenRedisVo vo = getUser(accessToken);
        dto.setUserId(vo.getId());
        return anchorService.longCompound(dto);
    }

    /**
     * 对话合成
     */
    @PostMapping("dialog")
    private ResultBean<Map<String, Object>> dialog(@RequestBody SpeechSynthesizerDto dto,
            @RequestHeader String accessToken) {
        return anchorService.dialog(dto, accessToken);
    }

    /**
     * 多人配音
     */
    @PostMapping("mergeAudio")
    public ResultBean<Map<String, Object>> mergeAudio(@RequestBody MergeVo mergeVo, @RequestHeader String accessToken) {
        return anchorService.mergeAudio(mergeVo, accessToken);
    }

    /**
     * 保存作品
     */
    @PostMapping("save")
    public ResultBean<Map<String, Object>> save(@RequestBody VoiceWorkVo dto, @RequestHeader String accessToken)
            throws CustomException {
        return anchorService.save(dto, accessToken);
    }

    /**
     * 上传文件
     */
    @PostMapping("input")
    public ResultBean<Object> input(MultipartFile file) {
        String url = ossService.putFile(null, file, "temp");
        return ResultBean.successfulResult(url);
    }

    /**
     * 新增设备音频并发送音频到设备
     *
     * @return
     */
    @PostMapping("send")
    @ResponseBody
    public ResultBean<Boolean> addDeviceVoice(@RequestHeader String accessToken,
            @RequestBody DeviceVoiceAddParam addParam) {
        return deviceService.addDeviceVoice(addParam);
    }

    /**
     * 上传录音
     */
    @PostMapping("uploadRecordFree")
    public ResultBean<Map<String, Object>> uploadRecordFree(@RequestPart("file") MultipartFile file,
            @RequestHeader String accessToken, RecordUploadReq uploadReq) {
        return anchorService.uploadRecordFree(file, accessToken, uploadReq);
    }

    /**
     * 获取AI分类
     * @return
     */
    @GetMapping("aiClassList")
    public ResultBean<List<AiClass>> aiClassList() {
        return anchorService.aiClassList();
    }

    /**
     * 获取AI风格
     * @return
     */
    @GetMapping("aiStyleList")
    public ResultBean<List<AiStyle>> aiStyleList() {
        return anchorService.aiStyleList();
    }

    /**
     * 获取AI语言
     * @return
     */
    @GetMapping("aiLanguageList")
    public ResultBean<List<AiLanguage>> aiLanguageList() {
        return anchorService.aiLanguageList();
    }

    /**
     * 上传视频
     * @param file
     * @param accessToken
     * @param name
     * @return
     */
    @PostMapping("uploadVideo")
    public ResultBean<Map<String, Object>> uploadVideo(@RequestPart("file") MultipartFile file,
            @RequestHeader String accessToken, String name) {
        return anchorService.uploadVideo(file, accessToken, name);
    }

    /**
     * 获取热门主播
     * @param accessToken
     * @return
     */
    @GetMapping("hotList")
    public ResultBean<List<AnchorVo>> hotList(@RequestHeader String accessToken) {
        return anchorService.hotList(accessToken);
    }
}
