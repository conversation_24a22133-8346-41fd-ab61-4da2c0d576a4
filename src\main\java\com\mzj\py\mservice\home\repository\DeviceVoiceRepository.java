package com.mzj.py.mservice.home.repository;

import com.mzj.py.mservice.home.entity.DeviceVoice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

public interface DeviceVoiceRepository extends JpaRepository<DeviceVoice,Long>, JpaSpecificationExecutor<DeviceVoice> {
    List<DeviceVoice> findByDeviceIdInAndVoiceWorkIdAndDelStatus(List<Long> deviceId,Long workId,Integer del);

    @Transactional
    @Modifying
    @Query(value = "update dub_device_voice set del_status = 1 where voice_work_id = ?1",nativeQuery = true)
    void editDelById(Long id);


    List<DeviceVoice> findAllByDelStatusAndVoiceWorkId(Integer del,Long workId);

    @Transactional
    @Modifying
    @Query(value = "update dub_device_voice set del_status = 1 where id = ?1 ",nativeQuery = true)
    Integer updateDelStatusById(Long id);

    @Transactional
    @Modifying
    @Query(value = "update dub_device_voice set sortby = ?2 where id = ?1 ",nativeQuery = true)
    Integer updateSortBy(Long id,Integer sortby);

    List<DeviceVoice> findByDeviceIdAndTitle(Long deviceId, String title);

    List<DeviceVoice> findByTitle(String title);

    @Modifying
    @Transactional
    @Query(value = "update DeviceVoice p set p.delStatus = 1 where p.id in (:collection)")
    int update(@Param("collection") Collection<Long> collection);
}
