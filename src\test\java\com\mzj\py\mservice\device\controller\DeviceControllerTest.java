package com.mzj.py.mservice.device.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.mservice.broadcastPlan.controller.BroadcastPlanController;
import com.mzj.py.mservice.common.ApiBaseController;
import com.mzj.py.mservice.device.service.DeviceService;
import com.mzj.py.mservice.device.vo.*;
import com.mzj.py.mservice.home.entity.Device;
import com.mzj.py.mservice.redis.RedisService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class DeviceControllerTest {

    @InjectMocks
    private DeviceController deviceController;

    @Mock
    private DeviceService deviceService;
    @Mock
    private RedisService redisService;
    private MockMvc mockMvc;
    private static final String ACCESS_TOKEN = "test-access-token";
    private static final String ACCESS_TOKEN_HEADER = "accessToken";

    private DeviceQueryVo mockDeviceQueryVo;
    private DeviceUnBindVo mockDeviceUnBindVo;
    private DeviceAddVo mockDeviceAddVo;
    private DeviceUpdateVo mockDeviceUpdateVo;
    private DeviceVoiceVo mockDeviceVoiceVo;
    private DeviceVoiceAddParam mockDeviceVoiceAddParam;
    private List<DevVoiceVo> mockDevVoiceVoList;

    @BeforeEach
    void setUp() {
        deviceController = spy(new DeviceController());
        // 注入依赖
        ReflectionTestUtils.setField(deviceController, "deviceService", deviceService);
        ReflectionTestUtils.setField(deviceController, "redisService", redisService);

        mockMvc = MockMvcBuilders.standaloneSetup(deviceController).build();

        mockDeviceQueryVo = new DeviceQueryVo();
        mockDeviceQueryVo.setKeyword("test");
        mockDeviceQueryVo.setPageSize(10);
        mockDeviceQueryVo.setPageNumber(1);

        mockDeviceUnBindVo = new DeviceUnBindVo();
        mockDeviceUnBindVo.setId(1L);
        mockDeviceUnBindVo.setShopId(1L);

        mockDeviceAddVo = new DeviceAddVo();
        mockDeviceAddVo.setName("Test Device");
        mockDeviceAddVo.setSn("TEST001");
        mockDeviceAddVo.setShopId(1L);

        mockDeviceUpdateVo = new DeviceUpdateVo();
        mockDeviceUpdateVo.setId(1L);
        mockDeviceUpdateVo.setName("Updated Device");
        mockDeviceUpdateVo.setSn("TEST001");

        mockDeviceVoiceVo = new DeviceVoiceVo();
        mockDeviceVoiceVo.setId(1L);
        mockDeviceVoiceVo.setDeviceId(1L);
        mockDeviceVoiceVo.setSpeed(50);
        mockDeviceVoiceVo.setVolume(50);
        mockDeviceVoiceVo.setPitch(50);
        mockDeviceVoiceVo.setBackgroundMusicVolume(30);
        mockDeviceVoiceVo.setVoiceUrl("test-voice.mp3");

        mockDeviceVoiceAddParam = new DeviceVoiceAddParam();
        mockDeviceVoiceAddParam.setDeviceIds(Arrays.asList(1L, 2L));
        mockDeviceVoiceAddParam.setTitle("Test Voice");
        mockDeviceVoiceAddParam.setContent("Test content");
        mockDeviceVoiceAddParam.setVoiceId(1L);
        mockDeviceVoiceAddParam.setBackgroundMusicId(1L);
        mockDeviceVoiceAddParam.setBackgroundMusicVolume(30);
        DevVoiceVo devVoiceVo = new DevVoiceVo();
        devVoiceVo.setName("test-voice.mp3");
        mockDevVoiceVoList = Arrays.asList(devVoiceVo);
        TokenRedisVo mockUser = new TokenRedisVo();
        mockUser.setId(1L);

        lenient().doReturn(Arrays.asList(1L, 2L)).when(deviceController).getShopIds(ACCESS_TOKEN);
        lenient().doReturn(mockUser).when(deviceController).getUser(ACCESS_TOKEN);
        lenient().doReturn(1L).when(deviceController).getShopId(ACCESS_TOKEN);
    }

    @Test
    @DisplayName("获取设备列表成功")
    void testList_Success() throws Exception {
        // Given
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("count", 1);
        responseData.put("result", Arrays.asList(new Device()));
        ResultBean<Map<String, Object>> result = ResultBean.successfulResult(responseData);

        when(deviceService.list(any(DeviceQueryVo.class))).thenReturn(result);

        // When & Then
        mockMvc.perform(post("/mini/device/list")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceQueryVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.resultData.count").value(1));

        verify(deviceService).list(argThat(queryVo -> queryVo.getShopIds().equals(Arrays.asList(1L, 2L)) &&
                queryVo.getCreateId().equals(1L)));
    }

    @Test
    @DisplayName("获取设备列表为空")
    void testList_EmptyResult() throws Exception {
        // Given
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("count", 0);
        responseData.put("result", Arrays.asList());
        ResultBean<Map<String, Object>> result = ResultBean.successfulResult(responseData);

        when(deviceService.list(any(DeviceQueryVo.class))).thenReturn(result);

        // When & Then
        mockMvc.perform(post("/mini/device/list")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceQueryVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.resultData.count").value(0));
    }

    @Test
    @DisplayName("解绑设备成功")
    void testUnBind_Success() throws Exception {
        // Given
        ResultBean<Boolean> result = ResultBean.successfulResult(true);
        when(deviceService.unBind(1L)).thenReturn(result);

        // When & Then
        mockMvc.perform(get("/mini/device/unBind/1")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.resultData").value(true));

        verify(deviceService).unBind(1L);
    }

    @Test
    @DisplayName("解绑设备-设备不存在")
    void testUnBind_DeviceNotFound() throws Exception {
        // Given
        ResultBean<Boolean> result = ResultBean.failedResultWithMsg("设备不存在");
        when(deviceService.unBind(999L)).thenReturn(result);

        // When & Then
        mockMvc.perform(get("/mini/device/unBind/999")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(false))
                .andExpect(jsonPath("$.msg").value("设备不存在"));

        verify(deviceService).unBind(999L);
    }

    @Test
    @DisplayName("解绑设备-缺少accessToken请求头")
    void testUnBind_MissingHeader() throws Exception {
        // Expect 400 Bad Request when the required accessToken header is absent
        mockMvc.perform(get("/mini/device/unBind/1"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("绑定门店成功")
    void testBindShop_Success() throws Exception {
        // Given
        ResultBean<Boolean> result = ResultBean.successfulResult(true);
        when(deviceService.bind(any(DeviceUnBindVo.class))).thenReturn(result);

        // When & Then
        mockMvc.perform(post("/mini/device/bindShop")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceUnBindVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.resultData").value(true));

        verify(deviceService).bind(any(DeviceUnBindVo.class));
    }

    @Test
    @DisplayName("绑定门店-门店不存在")
    void testBindShop_InvalidData() throws Exception {
        // Given
        ResultBean<Boolean> result = ResultBean.failedResultWithMsg("门店不存在");
        when(deviceService.bind(any(DeviceUnBindVo.class))).thenReturn(result);

        // When & Then
        mockMvc.perform(post("/mini/device/bindShop")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceUnBindVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(false))
                .andExpect(jsonPath("$.msg").value("门店不存在"));
    }

    @Test
    @DisplayName("绑定门店-缺少请求头")
    void testBindShop_MissingHeader() throws Exception {
        mockMvc.perform(post("/mini/device/bindShop")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceUnBindVo)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("新增设备成功")
    void testAdd_Success() throws Exception {
        // Given
        ResultBean<Boolean> result = ResultBean.successfulResult(true);
        when(deviceService.addOrUpdate(any(Device.class))).thenReturn(result);

        // When & Then
        mockMvc.perform(post("/mini/device/add")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceAddVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.resultData").value(true));

        verify(deviceService).addOrUpdate(argThat(device -> device.getName().equals("Test Device") &&
                device.getSn().equals("TEST001") &&
                device.getUserId().equals(1L) &&
                device.getShopId().equals(1L)));
    }

    @Test
    @DisplayName("新增设备-无门店ID使用默认")
    void testAdd_WithoutShopId() throws Exception {
        // Given
        mockDeviceAddVo.setShopId(null);
        ResultBean<Boolean> result = ResultBean.successfulResult(true);
        when(deviceService.addOrUpdate(any(Device.class))).thenReturn(result);

        // When & Then
        mockMvc.perform(post("/mini/device/add")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceAddVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true));

        verify(deviceService).addOrUpdate(argThat(device -> device.getShopId().equals(1L) // Should use default
                // shop ID
        ));
    }

    @Test
    @DisplayName("新增设备-缺少请求头")
    void testAdd_MissingHeader() throws Exception {
        mockMvc.perform(post("/mini/device/add")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceAddVo)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("修改设备成功")
    void testUpdate_Success() throws Exception {
        // Given
        ResultBean<Boolean> result = ResultBean.successfulResult(true);
        when(deviceService.addOrUpdate(any(Device.class))).thenReturn(result);

        // When & Then
        mockMvc.perform(post("/mini/device/update")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceUpdateVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.resultData").value(true));

        verify(deviceService).addOrUpdate(argThat(device -> device.getId().equals(1L) &&
                device.getName().equals("Updated Device") &&
                device.getUserId().equals(1L)));
    }

    @Test
    @DisplayName("修改设备-无门店ID使用默认")
    void testUpdate_WithoutShopId() throws Exception {

        mockDeviceUpdateVo.setShopId(null);
        ResultBean<Boolean> result = ResultBean.successfulResult(true);
        when(deviceService.addOrUpdate(any(Device.class))).thenReturn(result);

        mockMvc.perform(post("/mini/device/update")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceUpdateVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true));

        verify(deviceService).addOrUpdate(argThat(device -> device.getShopId().equals(1L) // Should use default

        ));
    }

    @Test
    @DisplayName("设备详情获取成功")
    void testDetail_Success() throws Exception {
        // Given
        DetailVo detailVo = new DetailVo();
        detailVo.setDeviceId(1L);
        detailVo.setDeviceName("Test Device");
        ResultBean<DetailVo> result = ResultBean.successfulResult(detailVo);
        when(deviceService.detail(1L, 10, 0)).thenReturn(result);

        // When & Then
        mockMvc.perform(get("/mini/device/detail")
                        .param("deviceId", "1")
                        .param("pageSize", "10")
                        .param("pageNumber", "0"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.resultData.deviceId").value(1))
                .andExpect(jsonPath("$.resultData.deviceName").value("Test Device"));

        verify(deviceService).detail(1L, 10, 0);
    }

    @Test
    @DisplayName("设备详情使用默认分页参数")
    void testDetail_WithDefaultParameters() throws Exception {
        // Given
        DetailVo detailVo = new DetailVo();
        detailVo.setDeviceId(1L);
        ResultBean<DetailVo> result = ResultBean.successfulResult(detailVo);
        when(deviceService.detail(1L, 10, 0)).thenReturn(result);

        // When & Then
        mockMvc.perform(get("/mini/device/detail")
                        .param("deviceId", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true));

        verify(deviceService).detail(1L, 10, 0); // Should use default values
    }

    @Test
    @DisplayName("设备详情-缺少设备ID")
    void testDetail_InvalidDeviceId() throws Exception {
        // Given
        ResultBean<DetailVo> result = ResultBean.failedResultWithMsg("设备id不能为空");
        lenient().when(deviceService.detail(null, 10, 0)).thenReturn(result);

        // When & Then
        mockMvc.perform(get("/mini/device/detail"))
                .andExpect(status().isBadRequest()); // Missing required parameter
    }

    @Test
    @DisplayName("设备详情含语音包列表成功")
    void testDetailWithVoiceList_Success() throws Exception {
        // Given
        DetailVo detailVo = new DetailVo();
        detailVo.setDeviceId(1L);
        ResultBean<DetailVo> result = ResultBean.successfulResult(detailVo);
        when(deviceService.deviceVoicedetail(1L, mockDevVoiceVoList)).thenReturn(result);

        // When & Then
        mockMvc.perform(post("/mini/device/detail/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDevVoiceVoList)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.resultData.deviceId").value(1));

        verify(deviceService).deviceVoicedetail(eq(1L), anyList());
    }

    @Test
    @DisplayName("更新设备语音成功")
    void testUpdateDeviceVoice_Success() throws Exception {
        // Given
        ResultBean<Boolean> result = ResultBean.successfulResult(true);
        when(deviceService.updateDeviceVoice(any(DeviceVoiceVo.class))).thenReturn(result);

        // When & Then
        mockMvc.perform(post("/mini/device/devicevoice")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceVoiceVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.resultData").value(true));

        verify(deviceService).updateDeviceVoice(any(DeviceVoiceVo.class));
    }

    @Test
    @DisplayName("更新设备语音-参数校验失败")
    void testUpdateDeviceVoice_ValidationError() throws Exception {
        // Given
        ResultBean<Boolean> result = ResultBean.failedResultWithMsg("设备id不能为空");
        when(deviceService.updateDeviceVoice(any(DeviceVoiceVo.class))).thenReturn(result);

        // When & Then
        mockMvc.perform(post("/mini/device/devicevoice")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceVoiceVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(false))
                .andExpect(jsonPath("$.msg").value("设备id不能为空"));
    }

    @Test
    @DisplayName("删除设备语音成功")
    void testDeleteDeviceVoice_Success() throws Exception {
        // Given
        ResultBean<Boolean> result = ResultBean.successfulResult(true);
        when(deviceService.deleteDeviceVoice(1L)).thenReturn(result);

        // When & Then
        mockMvc.perform(delete("/mini/device")
                        .param("id", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.resultData").value(true));

        verify(deviceService).deleteDeviceVoice(1L);
    }

    @Test
    @DisplayName("删除设备语音-设备与语音关系ID为空")
    void testDeleteDeviceVoice_InvalidId() throws Exception {
        // Given
        ResultBean<Boolean> result = ResultBean.failedResultWithMsg("设备与语音包关系表id不能为空");
        lenient().when(deviceService.deleteDeviceVoice(null)).thenReturn(result);

        // When & Then
        mockMvc.perform(delete("/mini/device"))
                .andExpect(status().isBadRequest()); // Missing required parameter
    }

    @Test
    @DisplayName("获取用户设备树成功")
    void testGetTreeUserDeviceSelect_Success() throws Exception {
        // Given
        DeviceTreeOptionVo treeOption = new DeviceTreeOptionVo();
        treeOption.setId(1L);
        treeOption.setLabel("Shop 1");
        List<DeviceTreeOptionVo> treeOptions = Arrays.asList(treeOption);
        ResultBean<List<DeviceTreeOptionVo>> result = ResultBean.successfulResult(treeOptions);
        when(deviceService.getTreeUserDeviceSelect(Arrays.asList(1L, 2L))).thenReturn(result);

        // When & Then
        mockMvc.perform(get("/mini/device/tree/my")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.resultData").isArray())
                .andExpect(jsonPath("$.resultData[0].id").value(1))
                .andExpect(jsonPath("$.resultData[0].label").value("Shop 1"));

        verify(deviceService).getTreeUserDeviceSelect(Arrays.asList(1L, 2L));
    }

    @Test
    @DisplayName("获取用户设备树为空")
    void testGetTreeUserDeviceSelect_EmptyResult() throws Exception {
        // Given
        ResultBean<List<DeviceTreeOptionVo>> result = ResultBean.successfulResult(Arrays.asList());
        when(deviceService.getTreeUserDeviceSelect(Arrays.asList(1L, 2L))).thenReturn(result);

        // When & Then
        mockMvc.perform(get("/mini/device/tree/my")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.resultData").isEmpty());
    }

    @Test
    @DisplayName("缺少accessToken请求头")
    void testMissingAccessTokenHeader() throws Exception {
        // When & Then
        mockMvc.perform(post("/mini/device/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceQueryVo)))
                .andExpect(status().isBadRequest()); // Should fail due to missing header
    }

    @Test
    @DisplayName("设备列表-服务异常")
    void testList_ServiceException() throws Exception {
        // Given
        when(deviceService.list(any(DeviceQueryVo.class)))
                .thenReturn(ResultBean.failedResultWithMsg("服务异常"));

        // When & Then
        mockMvc.perform(post("/mini/device/list")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceQueryVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(false))
                .andExpect(jsonPath("$.msg").value("服务异常"));
    }

    @Test
    @DisplayName("绑定门店-设备已绑定其他门店")
    void testBindShop_AlreadyBound() throws Exception {
        // Given
        ResultBean<Boolean> result = ResultBean.failedResultWithMsg("设备已绑定门店,请联系客服解绑");
        when(deviceService.bind(any(DeviceUnBindVo.class))).thenReturn(result);

        // When & Then
        mockMvc.perform(post("/mini/device/bindShop")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceUnBindVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(false))
                .andExpect(jsonPath("$.msg").value("设备已绑定门店,请联系客服解绑"));
    }

    @Test
    @DisplayName("新增设备-创建失败")
    void testAdd_ServiceException() throws Exception {
        // Given
        when(deviceService.addOrUpdate(any(Device.class)))
                .thenReturn(ResultBean.failedResultWithMsg("创建门店失败"));

        // When & Then
        mockMvc.perform(post("/mini/device/add")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceAddVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(false))
                .andExpect(jsonPath("$.msg").value("创建门店失败"));
    }

    @Test
    @DisplayName("修改设备-无此设备数据")
    void testUpdate_ServiceException() throws Exception {
        // Given
        when(deviceService.addOrUpdate(any(Device.class)))
                .thenReturn(ResultBean.failedResultWithMsg("无此设备数据"));

        // When & Then
        mockMvc.perform(post("/mini/device/update")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDeviceUpdateVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(false))
                .andExpect(jsonPath("$.msg").value("无此设备数据"));
    }

    @Test
    @DisplayName("设备详情-无此设备数据")
    void testDetail_ServiceException() throws Exception {
        // Given
        when(deviceService.detail(1L, 10, 0))
                .thenReturn(ResultBean.failedResultWithMsg("无此设备数据"));

        // When & Then
        mockMvc.perform(get("/mini/device/detail")
                        .param("deviceId", "1")
                        .param("pageSize", "10")
                        .param("pageNumber", "0"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(false))
                .andExpect(jsonPath("$.msg").value("无此设备数据"));
    }

    @Test
    @DisplayName("设备详情含语音-服务异常")
    void testDetailWithVoiceList_ServiceException() throws Exception {
        // Given
        when(deviceService.deviceVoicedetail(1L, mockDevVoiceVoList))
                .thenReturn(ResultBean.failedResultWithMsg("无此设备数据"));

        // When & Then
        mockMvc.perform(post("/mini/device/detail/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(mockDevVoiceVoList)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(false))
                .andExpect(jsonPath("$.msg").value("无此设备数据"));
    }

    @Test
    @DisplayName("更新设备语音-缺少必要参数")
    void testUpdateDeviceVoice_MissingParameters() throws Exception {
        // Given
        DeviceVoiceVo invalidVo = new DeviceVoiceVo();
        invalidVo.setId(null); // Missing required parameter

        ResultBean<Boolean> result = ResultBean.failedResultWithMsg("设备与语音表id不能为空");
        when(deviceService.updateDeviceVoice(any(DeviceVoiceVo.class))).thenReturn(result);

        // When & Then
        mockMvc.perform(post("/mini/device/devicevoice")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(invalidVo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(false))
                .andExpect(jsonPath("$.msg").value("设备与语音表id不能为空"));
    }

    @Test
    @DisplayName("删除设备语音-服务异常")
    void testDeleteDeviceVoice_ServiceException() throws Exception {
        // Given
        ResultBean<Boolean> result = ResultBean.failedResultWithMsg("设备与语音包关系表id不能为空");
        lenient().when(deviceService.deleteDeviceVoice(null)).thenReturn(result);

        // When & Then
        mockMvc.perform(delete("/mini/device")
                        .param("id", ""))
                .andExpect(status().isBadRequest()); // Invalid parameter
    }

    @Test
    @DisplayName("获取用户设备树-查询失败")
    void testGetTreeUserDeviceSelect_ServiceException() throws Exception {
        // Given
        when(deviceService.getTreeUserDeviceSelect(Arrays.asList(1L, 2L)))
                .thenReturn(ResultBean.failedResultWithMsg("查询失败"));

        // When & Then
        mockMvc.perform(get("/mini/device/tree/my")
                        .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(false))
                .andExpect(jsonPath("$.msg").value("查询失败"));
    }

    @Test
    @DisplayName("获取用户设备树-缺少请求头")
    void testGetTreeUserDeviceSelect_MissingHeader() throws Exception {
        mockMvc.perform(get("/mini/device/tree/my"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("设备详情含语音-空列表")
    void testDetailWithVoiceList_EmptyList() throws Exception {
        // Given
        DetailVo detailVo = new DetailVo();
        detailVo.setDeviceId(1L);
        ResultBean<DetailVo> result = ResultBean.successfulResult(detailVo);
        when(deviceService.deviceVoicedetail(eq(1L), anyList())).thenReturn(result);

        // When & Then
        mockMvc.perform(post("/mini/device/detail/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("[]")) // Empty list
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.resultData.deviceId").value(1));
    }

    @Test
    @DisplayName("更新设备语音-无效JSON")
    void testUpdateDeviceVoice_InvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/mini/device/devicevoice")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{invalid json}"))
                .andExpect(status().isBadRequest());
    }
}