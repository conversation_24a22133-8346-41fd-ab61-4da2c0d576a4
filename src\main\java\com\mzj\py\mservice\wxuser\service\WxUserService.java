package com.mzj.py.mservice.wxuser.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.StatusCode;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.UrlBuilder;
import com.mzj.py.mservice.home.dto.AnchorDTO;
import com.mzj.py.mservice.home.entity.WxKey;
import com.mzj.py.mservice.home.repository.BackgroundMusicRepository;
import com.mzj.py.mservice.home.repository.DubAnchorRepository;
import com.mzj.py.mservice.home.repository.LongAnchorRepository;
import com.mzj.py.mservice.home.repository.WxKeyRepository;
import com.mzj.py.mservice.home.vo.MusicVo;
import com.mzj.py.mservice.home.vo.TemplateVo;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.token.service.TokenService;
import com.mzj.py.mservice.wxuser.controller.request.*;
import com.mzj.py.mservice.wxuser.entity.*;
import com.mzj.py.mservice.wxuser.repository.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.AlgorithmParameters;
import java.security.Security;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class WxUserService {
    private final Logger LOG = LoggerFactory.getLogger(WxUserService.class);

    public static final String JS_CODE_2_SESSION = "https://api.weixin.qq.com/sns/jscode2session"; // 获取到用户的openId

    @Value("${wx.pay.appid}")
    private String appid;
    @Value("${wx.app.secret}")
    private String secret;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private WxUserRepository wxUserRepository;

    @Autowired
    private RedisService redisService;

    @Autowired
    private WxKeyRepository wxKeyRepository;

    @Autowired
    private BackgroundMusicRepository backgroundMusicRepository;

    @Autowired
    private LongFollowAnchorRepository longFollowAnchorRepository;

    @Autowired
    private FollowAnchorRepository followAnchorRepository;

    @Autowired
    private UserFollowBgmRepository userFollowBgmRepository;

    @Autowired
    private LongAnchorRepository longAnchorRepository;

    @Autowired
    private UserTextTemplateRepository userTextTemplateRepository;

    @Autowired
    private DubAnchorRepository dubAnchorRepository;

    public ResultBean<Map<String, Object>> userOpenId(String appId, String code) {
        LOG.info("WxUserService userOpenId appId = {}, code = {}", appId, code);
        ResultBean<Map<String, Object>> resultBean = new ResultBean<>();
        try {
            resultBean.setCode(StatusCode.SUCCESS_CODE_10000.getErrorCode());
            resultBean.setMsg(StatusCode.SUCCESS_CODE_10000.getErrorMsg());
            resultBean.setResultData(Collections.emptyMap());

            // 数据校验
            if (StringUtils.isBlank(code)) {
                resultBean.setCode(StatusCode.ERROR_CODE_10004.getErrorCode());
                resultBean.setMsg("CODE不能为空");
                return resultBean;
            }
            if (StringUtils.isBlank(appId)) {
                resultBean.setCode(StatusCode.ERROR_CODE_10004.getErrorCode());
                resultBean.setMsg("小程序appId不能为空");
                return resultBean;
            }
            if (!appid.equals(appId)) {
                resultBean.setCode(StatusCode.ERROR_CODE_10004.getErrorCode());
                resultBean.setMsg("小程序appId错误");
                return resultBean;
            }

            UrlBuilder api = new UrlBuilder(JS_CODE_2_SESSION);
            api.param("appid", appid)
                    .param("secret", secret)
                    .param("js_code", code).param("grant_type", "authorization_code");
            String result = restTemplate.getForObject(api.toString(), String.class);
            JSONObject resultObject = JSONObject.parseObject(result);
            if (resultObject == null || ObjectUtil.isEmpty(resultObject.get("openid"))) {
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("用户openId为空");
                return resultBean;
            }
            String openId = resultObject.getString("openid");
            String unionId = resultObject.getString("unionid");

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("openId", openId);
            resultMap.put("unionId", unionId);
            resultBean.setResultData(resultMap);
        } catch (Exception e) {
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg(StatusCode.ERROR_CODE_30001.getErrorMsg());
            return resultBean;
        }

        return resultBean;
    }

    /**
     * 授权登录
     */
    public ResultBean<Object> userLoginRegister(UserRegistReq vo) {
        LOG.info("WxUserService userOpenId appId = {}, code = {}", vo.getAppId(), vo.getCode());
        ResultBean<Object> resultBean = new ResultBean<>();
        resultBean.setCode(StatusCode.SUCCESS_CODE_10000.getErrorCode());
        resultBean.setMsg(StatusCode.SUCCESS_CODE_10000.getErrorMsg());
        resultBean.setResultData(Collections.emptyMap());
        try {
            if (StringUtils.isBlank(vo.getCode())) {
                resultBean.setCode(StatusCode.ERROR_CODE_10004.getErrorCode());
                resultBean.setMsg("CODE不能为空");
                return resultBean;
            }
            if (StringUtils.isBlank(vo.getAppId())) {
                resultBean.setCode(StatusCode.ERROR_CODE_10004.getErrorCode());
                resultBean.setMsg("小程序appId不能为空");
                return resultBean;
            }
            WxKey wxKey = wxKeyRepository.findByAppId(vo.getAppId());
            if (wxKey == null) {
                resultBean.setCode(StatusCode.ERROR_CODE_10004.getErrorCode());
                resultBean.setMsg("小程序appId错误");
                return resultBean;
            }
            String appid = wxKey.getAppId();
            String secret = wxKey.getSecret();
            UrlBuilder api = new UrlBuilder(JS_CODE_2_SESSION);
            api.param("appid", appid)
                    .param("secret", secret)
                    .param("js_code", vo.getCode()).param("grant_type", "authorization_code");
            String result = restTemplate.getForObject(api.toString(), String.class);
            JSONObject resultObject = JSONObject.parseObject(result);
            String sessionKey = resultObject.getString("session_key");// 密钥
            LOG.info("sessionKey={}, result={},appid={},secret={}", sessionKey, result, appid, secret);
            if (resultObject.containsKey("errcode")) {
                LOG.warn("WxUserService userOpenId openId is falis resultObject = {}", resultObject.toJSONString());
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("已经登陆成功");
                return resultBean;
            }
            Map<String, Object> resultMap = new HashMap<>();
            String openId = resultObject.getString("openid");
            String unionId = resultObject.getString("unionid");
            resultMap.put("openId", openId);
            resultMap.put("unionId", unionId);
            resultMap.put("sessionKey", sessionKey);
            LOG.info("小程序OPENID登录注册, UserVo={}", JSON.toJSONString(vo));
            resultBean.setCode(StatusCode.SUCCESS_CODE_10000.getErrorCode());
            resultBean.setMsg(StatusCode.SUCCESS_CODE_10000.getErrorMsg());
            resultBean.setResultData(Collections.emptyMap());
            if (StringUtils.isEmpty(unionId) || StringUtils.isEmpty(openId)) {
                LOG.error("登录参数为空, unionId={} openId={}", unionId, openId);
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg(StatusCode.ERROR_CODE_10004.getErrorMsg());
                return resultBean;
            }
            Map<String, Object> map = new HashMap<>();
            WxUser entity = wxUserRepository.findByUnionid(unionId);
            if (entity == null) {
                LOG.info("小程序登录 用户未找到, 注册用户, getUnionid={}", unionId);
                entity = new WxUser();
                entity.setOpenid(openId);
                entity.setNickname(vo.getNickName() != null ? vo.getNickName() : "用户" + openId.substring(openId.length() - 4, openId.length()));
                entity.setPhone(vo.getPhone());
                entity.setGender(vo.getSex());
                entity.setArea(vo.getCountry() + vo.getProvince() + vo.getCity());
                entity.setAuthTime(new Date());
                entity.setUnionid(unionId);
                wxUserRepository.save(entity);
            } else {
                entity.setAuthTime(new Date());
                wxUserRepository.save(entity);
                LOG.info("小程序登录, 已注册用户登录, openid={}", openId);
            }
            if (entity.getNickname() != null) {
                entity.setNicknameToString(entity.getNickname());
            }
            TokenRedisVo redisVo = new TokenRedisVo();
            BeanUtils.copyProperties(entity, redisVo);
            if (entity.getNickname() != null) {
                redisVo.setNicknames(entity.getNickname());
            }
            String token = tokenService.authToken(openId, redisVo);
            map.put("accessToken", token);
            map.put("user", JSON.toJSONString(entity));
            return ResultBean.successfulResult(map);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg(StatusCode.ERROR_CODE_30001.getErrorMsg());
        }
        return resultBean;
    }

    /**
     * 授权获取手机号码
     *
     * @param sessionKey
     * @param encryptedData
     * @param iv
     * @return
     */
    public ResultBean<Map<String, Object>> userPhone(String accessToken, String sessionKey, String encryptedData, String iv) {
        LOG.info("WxUserService userPhone sessionKey = {}, encryptedData = {}, iv = {}", sessionKey, encryptedData, iv);
        ResultBean<Map<String, Object>> resultBean = new ResultBean<>();
        try {
            resultBean.setCode(StatusCode.SUCCESS_CODE_10000.getErrorCode());
            resultBean.setMsg(StatusCode.SUCCESS_CODE_10000.getErrorMsg());
            resultBean.setResultData(Collections.emptyMap());
            Map<String, Object> returnMap = new HashMap<>();

            if (StringUtils.isBlank(encryptedData)) {
                resultBean.setCode(StatusCode.ERROR_CODE_10004.getErrorCode());
                resultBean.setMsg("encryptedData不能为空");
                return resultBean;
            }

            if (StringUtils.isBlank(iv)) {
                resultBean.setCode(StatusCode.ERROR_CODE_10004.getErrorCode());
                resultBean.setMsg("iv不能为空");
                return resultBean;
            }

            if (StringUtils.isBlank(sessionKey)) {
                resultBean.setCode(StatusCode.ERROR_CODE_10004.getErrorCode());
                resultBean.setMsg("sessionKey不能为空");
                return resultBean;
            }
            TokenRedisVo tokenRedisVo = redisService.findTokenVo(accessToken);
            if (tokenRedisVo == null) {
                resultBean.setCode(StatusCode.ERROR_CODE_10004.getErrorCode());
                resultBean.setMsg("登录信息获取失败");
                return resultBean;
            }
            String openid = tokenRedisVo.getOpenid();
            JSONObject object = decryptionWxData(encryptedData, sessionKey, iv);
            LOG.info("WxUserService userPhone object = {}", object);
            String phone = object.get("phoneNumber") == null ? "" : object.get("phoneNumber").toString();

            // 验证并注册新用户
            WxUser user = wxUserRepository.findByOpenid(openid);
            if (user == null) {
                user = new WxUser();
                user.setOpenid(openid);
                user.setPhone(phone);
                user.setAuthTime(new Date());
                wxUserRepository.save(user);
                LOG.info("WxUserService user register = {}", JSON.toJSONString(user));
            } else {
                user.setPhone(phone);
                wxUserRepository.save(user);
                LOG.info("WxUserService user already exist = {}", JSON.toJSONString(user));
            }
            tokenRedisVo.setPhone(phone);
            tokenService.updateAuthTokenInfo(accessToken, tokenRedisVo);
            returnMap.put("phone", phone);
            resultBean.setResultData(returnMap);
            return resultBean;
        } catch (Exception e) {
            LOG.error("WxUserService userPhone Exception = {}", e);
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg(StatusCode.ERROR_CODE_30001.getErrorMsg());
            return resultBean;
        }

    }

    /**
     * 小程序解密微信数据
     *
     * @param encryptedData
     * @param sessionKey
     * @param iv
     * @return
     */
    public JSONObject decryptionWxData(String encryptedData, String sessionKey, String iv) {
        // 被加密的数据
        byte[] dataByte = Base64.decodeBase64(encryptedData);
        // 加密秘钥
        byte[] keyByte = Base64.decodeBase64(sessionKey);
        // 偏移量
        byte[] ivByte = Base64.decodeBase64(iv);

        try {
            // 如果密钥不足16位，那么就补足. 这个if 中的内容很重要
            int base = 16;
            if (keyByte.length % base != 0) {
                int groups = keyByte.length / base + 1;
                byte[] temp = new byte[groups * base];
                Arrays.fill(temp, (byte) 0);
                System.arraycopy(keyByte, 0, temp, 0, keyByte.length);
                keyByte = temp;
            }
            // 初始化
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
            SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
            AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
            parameters.init(new IvParameterSpec(ivByte));
            cipher.init(Cipher.DECRYPT_MODE, spec, parameters);// 初始化
            byte[] resultByte = cipher.doFinal(dataByte);
            if (null != resultByte && resultByte.length > 0) {
                String result = new String(resultByte, StandardCharsets.UTF_8);
                LOG.info("WxUserService decryptionWxData result = {}", result);
                return JSONObject.parseObject(result);
            }
        } catch (Exception e) {
            LOG.error("WxUserService decryptionWxData Exception = {}", e);
        }
        return null;
    }

    public ResultBean updatePhone(Long userId, String phone) {
        Optional<WxUser> user = wxUserRepository.findById(userId);
        if (user.isPresent()) {
            WxUser wxUser = user.get();
            wxUser.setPhone(phone);
            wxUserRepository.save(wxUser);
            return ResultBean.successfulResult(true);
        }
        return ResultBean.failedResult(false);
    }


    public ResultBean<Map<String, Object>> getMyLongAnchor(String accessToken) {
        Map<String, Object> returnMap = new HashMap<>(1);
        TokenRedisVo vo = redisService.findTokenVo(accessToken);
        List<AnchorDTO> anchorVoList = longAnchorRepository.taijia(vo.getId());
        returnMap.put("result", anchorVoList);
        return ResultBean.successfulResult(returnMap);
    }

    public ResultBean<Object> followAnchor(String accessToken, FollowAnchorReq followAnchorReq) {
        Long anchorId = followAnchorReq.getAnchorId();
        TokenRedisVo vo = redisService.findTokenVo(accessToken);
        List<FollowAnchor> vipFollowAnchorList = followAnchorRepository.findByAnchorIdAndUserId(anchorId, vo.getId());
        if (vipFollowAnchorList.size() == 0) {
            FollowAnchor vipFollowAnchor = new FollowAnchor();
            vipFollowAnchor.setAnchorId(anchorId);
            vipFollowAnchor.setUserId(vo.getId());
            followAnchorRepository.save(vipFollowAnchor);
        } else {
            followAnchorRepository.delete(vipFollowAnchorList.get(0));
        }
        return ResultBean.successfulResult(null);
    }

    public ResultBean<Object> followLongAnchor(String accessToken, FollowAnchorReq followAnchorReq) {
        Long anchorId = followAnchorReq.getAnchorId();
        TokenRedisVo vo = redisService.findTokenVo(accessToken);
        List<LongFollowAnchor> longFollowAnchors = longFollowAnchorRepository.findByAnchorIdAndUserId(anchorId, vo.getId());
        if (longFollowAnchors.size() == 0) {
            LongFollowAnchor longFollowAnchor = new LongFollowAnchor();
            longFollowAnchor.setAnchorId(anchorId);
            longFollowAnchor.setUserId(vo.getId());
            longFollowAnchorRepository.save(longFollowAnchor);
        } else {
            longFollowAnchorRepository.delete(longFollowAnchors.get(0));
        }
        return ResultBean.successfulResult(null);
    }

    public ResultBean<Object> followBgm(String accessToken, FollowBgmReq followBgmReq) {
        Long bgmId = followBgmReq.getBgmId();
        TokenRedisVo vo = redisService.findTokenVo(accessToken);
        List<UserFollowBgm> userFollowBgms = userFollowBgmRepository.findByBgmIdAndUserId(bgmId, vo.getId());
        if (userFollowBgms.size() == 0) {
            UserFollowBgm userFollowBgm = new UserFollowBgm();
            userFollowBgm.setBgmId(bgmId);
            userFollowBgm.setUserId(vo.getId());
            userFollowBgmRepository.save(userFollowBgm);
        } else {
            userFollowBgmRepository.delete(userFollowBgms.get(0));
        }
        return ResultBean.successfulResult(null);
    }

    public ResultBean<Map<String, Object>> getFollowBgm(String accessToken) {
        Map<String, Object> returnMap = new HashMap<>(1);
        TokenRedisVo vo = redisService.findTokenVo(accessToken);
        List<MusicVo> followBgms = backgroundMusicRepository.findFollowBgm(vo.getId());
        returnMap.put("result", followBgms);
        return ResultBean.successfulResult(returnMap);
    }

    public ResultBean<Object> addTextTemplate(String accessToken, TextTemplateAddReq textTemplateAddReq) {
        TokenRedisVo vo = redisService.findTokenVo(accessToken);
        Long userId = vo.getId();
        UserTextTemplate userTextTemplate = new UserTextTemplate();
        userTextTemplate.setTextContent(textTemplateAddReq.getTextContent());
        userTextTemplate.setUserId(userId);
        if (textTemplateAddReq.getId() != null) {
            userTextTemplate.setId(textTemplateAddReq.getId());
        }
        userTextTemplateRepository.save(userTextTemplate);
        return ResultBean.successfulResult(true);
    }

    public ResultBean<Map<String, Object>> getTextTemplate(String accessToken) {
        TokenRedisVo vo = redisService.findTokenVo(accessToken);
        Long userId = vo.getId();
        List<UserTextTemplate> userTextTemplateList = userTextTemplateRepository.findByUserId(userId);
        List<TemplateVo> templateVoList = userTextTemplateList.stream().map(obj -> {
            TemplateVo templateVo = new TemplateVo();
            templateVo.setContent(obj.getTextContent());
            templateVo.setId(obj.getId());
            return templateVo;
        }).collect(Collectors.toList());
        Map<String, Object> returnMap = new HashMap<>(1);
        returnMap.put("result", templateVoList);
        return ResultBean.successfulResult(returnMap);
    }

    public ResultBean<Object> delTextTemplate(String accessToken, TextTemplateDelReq textTemplateDelReq) {
        TokenRedisVo vo = redisService.findTokenVo(accessToken);
        Long userId = vo.getId();
        userTextTemplateRepository.deleteByIdAndUserId(textTemplateDelReq.getId(), userId);
        return ResultBean.successfulResult(true);
    }

    public ResultBean<Map<String, Object>> getMyVipAnchor(String accessToken) {
        Map<String, Object> returnMap = new HashMap<>(1);
        TokenRedisVo vo = redisService.findTokenVo(accessToken);
        List<AnchorDTO> anchorVoList = dubAnchorRepository.sailuo(vo.getId());
        returnMap.put("result", anchorVoList);
        return ResultBean.successfulResult(returnMap);
    }

}
