package com.mzj.py.commons;

import java.io.Serializable;
import java.util.*;

/**
 * 统一返回结果
 *
 * <AUTHOR>
 * @date: 2019-12-04
 */
public class ResultBean<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 提示信息
     */
    private String msg;
    /**
     * 返回状态码
     */
    private String code;
    /**
     * 结果数据
     */
    private T resultData;

    public ResultBean() {
        super();
    }

    public ResultBean(T resultData) {
        super();
        this.resultData = resultData;
    }

    public ResultBean(Throwable e) {
        super();
        this.msg = e.getMessage();
    }

    public T getResultData() {
        return resultData;
    }

    public void setResultData(T resultData) {
        this.resultData = resultData;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String toString() {
        return "ResultBean [msg=" + msg + ", code=" + code + ", resultData=" + resultData + "]";
    }

    /**
     * 10000 --- 成功
     *
     * @param data
     * @return
     * <AUTHOR>
     */
    public static <T> ResultBean<T> successfulResult(T data) {
        ResultBean<T> resultBean = new ResultBean<>();
        resultBean.setCode(StatusCode.SUCCESS_CODE_10000.getErrorCode());
        resultBean.setMsg(StatusCode.SUCCESS_CODE_10000.getErrorMsg());
        resultBean.setResultData(data);
        return resultBean;
    }

    /**
     * 成功
     *
     * @param msg
     * @param data
     * @return
     * <AUTHOR>
     */
    public static <T> ResultBean<T> successfulResultWithMsg(String msg, T data) {
        ResultBean<T> resultBean = new ResultBean<>();
        resultBean.setCode(StatusCode.SUCCESS_CODE_10000.getErrorCode());
        resultBean.setMsg(msg);
        resultBean.setResultData(data);
        return resultBean;
    }

    /**
     * 10001 --- 失败
     *
     * @param data
     * @return
     * <AUTHOR>
     */
    public static <T> ResultBean<T> failedResult(T data) {
        ResultBean<T> resultBean = new ResultBean<>();
        resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
        resultBean.setMsg(StatusCode.ERROR_CODE_10001.getErrorMsg());
        resultBean.setResultData(data);
        return resultBean;
    }

    /**
     * 30001 --- 系统异常，请稍后再试
     *
     * @return
     * <AUTHOR>
     */
    public static <T> ResultBean<T> failedResultOfException() {
        ResultBean<T> resultBean = new ResultBean<>();
        resultBean.setCode(StatusCode.ERROR_CODE_30001.getErrorCode());
        resultBean.setMsg(StatusCode.ERROR_CODE_30001.getErrorMsg());
        return resultBean;
    }

    /**
     * 失败10001
     *
     * @param msg
     * @return
     * <AUTHOR>
     */
    public static <T> ResultBean<T> failedResultWithMsg(String msg) {
        ResultBean<T> resultBean = new ResultBean<>();
        resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
        resultBean.setMsg(msg);
        return resultBean;
    }

    /**
     * 参数错误10004
     *
     * @param msg
     * @return
     * <AUTHOR>
     */
    public static <T> ResultBean<T> failedResultOfParamWithMsg(String msg) {
        ResultBean<T> resultBean = new ResultBean<>();
        resultBean.setCode(StatusCode.ERROR_CODE_10004.getErrorCode());
        resultBean.setMsg(msg);
        return resultBean;
    }

    /**
     * 失败
     *
     * @param code
     * @param msg
     * @return
     * <AUTHOR>
     */
    public static <T> ResultBean<T> failedResultWithCodeAndMsg(String code, String msg) {
        ResultBean<T> resultBean = new ResultBean<>();
        resultBean.setCode(code);
        resultBean.setMsg(msg);
        return resultBean;
    }

    /**
     * 40001 --- accessToken失效
     *
     * @return
     * <AUTHOR>
     */
    public static <T> ResultBean<T> failedResultOfToken() {
        ResultBean<T> resultBean = new ResultBean<>();
        resultBean.setCode(StatusCode.ERROR_CODE_40001.getErrorCode());
        resultBean.setMsg(StatusCode.ERROR_CODE_40001.getErrorMsg());
        return resultBean;
    }

    public Boolean isOk() {

        return this.getCode().equals(StatusCode.SUCCESS_CODE_10000.getErrorCode());
    }

    public static ResultBean<Map<String, Object>> getResultMap(Integer size, Object resultData) {
        ResultBean<Map<String, Object>> resultBean = new ResultBean<>();
        Map<String, Object> map = new HashMap<>();
        // Keep backward-compatibility with existing usages of "count" while
        // also providing a more semantic alias "total" for new code/tests.
        Integer finalSize = Objects.isNull(size) ? 0 : size;
        map.put("count", finalSize);
        map.put("total", finalSize);
        map.put("result", Objects.isNull(resultData) ? new ArrayList<>() : resultData);
        resultBean.setResultData(map);
        resultBean.setCode(StatusCode.SUCCESS_CODE_10000.getErrorCode());
        resultBean.setMsg(StatusCode.SUCCESS_CODE_10000.getErrorMsg());
        return resultBean;
    }

    public static ResultBean<Map<String, Object>> getResultMap() {
        return getResultMap(0, new ArrayList<>());
    }

}
