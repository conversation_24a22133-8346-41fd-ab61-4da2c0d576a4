package com.mzj.py.mservice.home.entity;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 设备表
 * @author: duanjinze
 * @date: 2022/11/10 13:35
 * @version: 1.0
 */
@Data
@Entity
@Table(name = "dub_device")
public class Device {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 设备名称
     */
    @Basic
    @Column(name = "name")
    private String name;

    /**
     * 设备SN
     */
    @Basic
    @Column(name = "sn")
    private String sn;

    /**
     * 用户
     */
    @Basic
    @Column(name = "user_id")
    private Long userId;
    /**
     * 门店
     */
    @Basic
    @Column(name = "shop_id")
    private Long shopId;

    /**
     * 音量
     */
    @Basic
    @Column(name = "volume")
    private Integer volume;

    /**
     * 在线状态 0离线 1在线
     */
    @Basic
    @Column(name = "status",columnDefinition = "smallint")
    private Integer status;

    /**
     * 绑定状态 0未绑定 1已绑定
     */
    @Basic
    @Column(name = "bind_status",columnDefinition = "smallint")
    private Integer bindStatus;

    /**
     * 使用状态 0停用 1使用
     */
    @Basic
    @Column(name = "use_status",columnDefinition = "smallint")
    private Integer useStatus;

    /**
     * 删除状态 0未删除 1已删除
     */
    @Basic
    @Column(name = "del_status",columnDefinition = "smallint")
    private Integer delStatus;

    /**
     * 创建人
     */
    @Basic
    @Column(name = "create_id")
    private Long createId;

    /**
     * 创建时间
     */
    @Basic
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 在线状态更新时间
     */
    @Basic
    @Column(name = "update_status_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateStatusTime;
}
