package com.mzj.py.mservice.device.controller;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.mservice.common.ApiBaseController;
import com.mzj.py.mservice.device.service.RemoteDeviceService;
import com.mzj.py.mservice.device.vo.DelAudioParams;
import com.mzj.py.mservice.device.vo.UpdateVolumeParams;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 远程设备控制器
 *
 * <AUTHOR>
 * @date: 2025/3/20
 * @description:
 */
@Controller
@Scope("prototype")
@RequestMapping("/mini/device")
public class RemoteDeviceController extends ApiBaseController {

    @Resource
    private RemoteDeviceService remoteDeviceService;

    /**
     * 更新音量
     *
     * @return
     */
    @PostMapping("/updateVolume")
    @ResponseBody
    public ResultBean<Object> updateVolume(@RequestBody UpdateVolumeParams params, @RequestHeader String accessToken) {
        return remoteDeviceService.updateVolume(params, super.getUser(accessToken).getId());
    }

    /**
     * 删除音频
     *
     * @return
     */
    @PostMapping("/delAudio")
    @ResponseBody
    public ResultBean<Object> delAudio(@RequestBody DelAudioParams params, @RequestHeader String accessToken) {
        return remoteDeviceService.delAudio(params, super.getUser(accessToken).getId());
    }

    /**
     * 获取时间
     * -- 应该是硬件开机获取时间吧
     *
     * @return
     */
    @GetMapping("/getTime")
    @ResponseBody
    public ResultBean<Object> getTime(@RequestParam("deviceId") Long deviceId, @RequestHeader String accessToken) {
        return remoteDeviceService.getTime(deviceId, super.getUser(accessToken).getId());
    }

    /**
     * 获取音乐列表
     *
     * @return
     */
    @GetMapping("/getMusic")
    @ResponseBody
    public ResultBean<Object> getMusic(@RequestParam("deviceId") Long deviceId, @RequestHeader String accessToken) {
        return remoteDeviceService.getMusic(deviceId, super.getUser(accessToken).getId());
    }

    /**
     * 试听音频
     *
     * @return
     */
    @GetMapping("/listenAudio")
    @ResponseBody
    public ResultBean<Object> listenAudio(@RequestParam("deviceId") Long deviceId, @RequestParam("audioName") String audioName, @RequestHeader String accessToken) {
        return remoteDeviceService.listenAudio(deviceId, audioName, super.getUser(accessToken).getId());
    }

    /**
     * 获取剩余空间
     *
     * @return
     */
    @GetMapping("/getSpace")
    @ResponseBody
    public ResultBean<Object> getSpace(@RequestParam("deviceId") Long deviceId, @RequestHeader String accessToken) {
        return remoteDeviceService.getSpace(deviceId, super.getUser(accessToken).getId());
    }

    /**
     * 获取音量
     *
     * @return
     */
    @GetMapping("/getVolume")
    @ResponseBody
    public ResultBean<Object> getVolume(@RequestParam("deviceId") Long deviceId, @RequestHeader String accessToken) {
        return remoteDeviceService.getVolume(deviceId, super.getUser(accessToken).getId());
    }

    /**
     * 定时播放
     *
     * @return
     */
    @GetMapping("/setTimePlay")
    @ResponseBody
    public ResultBean<Object> setTimePlay(@RequestParam("deviceId") Long deviceId, @RequestParam("audioName") String audioName, @RequestHeader String accessToken) {
        return remoteDeviceService.setTimePlay(deviceId, audioName);
    }

    /**
     * 复制音频
     *
     * @return
     */
    @GetMapping("/transmissionAudio")
    @ResponseBody
    public ResultBean<Object> transmissionAudio(@RequestParam("deviceId") Long deviceId, @RequestHeader String accessToken) {
        return remoteDeviceService.transmissionAudio(deviceId);
    }

    /**
     * 发送音频
     *
     * @return
     */
    @GetMapping("/sendAudio")
    @ResponseBody
    public ResultBean<Object> sendAudio(@RequestParam("deviceId") Long deviceId, @RequestParam("audioUrl") String audioUrl,
                                        @RequestParam("name") String name, @RequestHeader String accessToken) {
        return remoteDeviceService.sendAudio(deviceId, audioUrl, super.getUser(accessToken).getId(), name);
    }

    /**
     * 扫描蓝牙
     *
     * @return
     */
    @GetMapping("/scanBluetooth")
    @ResponseBody
    public ResultBean<Object> scanBluetooth(@RequestParam("deviceId") Long deviceId, @RequestHeader String accessToken) {
        return remoteDeviceService.scanBluetooth(deviceId, super.getUser(accessToken).getId());
    }

    /**
     * 获取蓝牙 列表
     *
     * @return
     */
    @GetMapping("/bluetoothList")
    @ResponseBody
    public ResultBean<Object> getBluetooth(@RequestParam("deviceId") Long deviceId, @RequestHeader String accessToken) {
        return remoteDeviceService.getBluetooth(deviceId, super.getUser(accessToken).getId());
    }
}
