package com.mzj.py.mservice.device.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 设备表
 * @author: duanjinze
 * @date: 2022/11/10 13:35
 * @version: 1.0
 */
@Data
public class DeviceUpdateVo {

    // 设备Id
    @NotNull(message = "设备ID不能为空")
    private Long id;

    /**
     * 设备名称
     */
    @NotEmpty(message = "设备名称不能为空")
    private String name;

    /**
     * 设备SN
     */
    @NotEmpty(message = "设备SN不能为空")
    private String sn;


    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 音量
     */
    private Integer volume = 1;

}
