package com.mzj.py.mservice.broadcastPlan.service;

import cn.hutool.core.collection.CollUtil;
import com.mzj.py.aop.BroadPlanPermission;
import com.mzj.py.aop.DevicePermission;
import com.mzj.py.aop.StorePermission;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.mservice.broadcastPlan.entity.BroadcastPlan;
import com.mzj.py.mservice.broadcastPlan.entity.BroadcastPlanVoiceWorkRef;
import com.mzj.py.mservice.broadcastPlan.repository.BroadcastPlanRepository;
import com.mzj.py.mservice.broadcastPlan.repository.BroadcastPlanVoiceWorkRepository;
import com.mzj.py.mservice.broadcastPlan.vo.BroadcastPlanVo;
import com.mzj.py.mservice.broadcastPlan.vo.dto.BroadcastPlanAddDto;
import com.mzj.py.mservice.redis.RedisService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class BroadcastPlanService {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private RedisService redisService;
    @Autowired
    private BroadcastPlanRepository broadcastPlanRepository;
    @Autowired
    private BroadcastPlanVoiceWorkRepository broadcastPlanVoiceWorkRepository;

    public ResultBean<Map<String, Object>> list(Long shopId, Integer pageSize, Integer pageNumber) {
        List<Object> args = new ArrayList<>();
        List<String> query = new ArrayList<>();
        if (shopId != null) {
            query.add(" s.id = ? ");
            args.add(shopId);
        }

        StringBuilder where = new StringBuilder();
        if (CollUtil.isNotEmpty(query)) {
            where.append(" WHERE ");
            where.append(String.join(" AND ", query));
        }
        String sql = "SELECT " +
                "b.id, " +
                "b.start_time,   " +
                "b.end_time,   " +
                "b.create_time createdTime," +
                "s.id storeId," +
                "s.shop_name store," +
                "GROUP_CONCAT(DISTINCT(d.`id`) ) as deviceIds, " +
                "GROUP_CONCAT(DISTINCT(d.`sn`)) as deviceSn, " +
                "group_concat(DISTINCT(v.`id`))  as musicIds, " +
                "group_concat(DISTINCT(v.`title`))  as musicName, " +
                "b.start_date,b.end_date,b.type,b.interval_time  " +
                "FROM   dub_broadcast_plan b " +
                "left join dub_shop s on b.shop_id = s.id " +
                "left join dub_device d on  FIND_IN_SET(d.id,b.device_ids) " +
                "left join dub_broadcast_plan_voice_work_ref br on br.plan_id = b.id " +
                "left join dub_voice_work v on v.id = br.voice_work_id ";
        where.append("group by b.id ");
        where.append("order by b.create_time DESC  ");
        Long count = jdbcTemplate.queryForObject("select count(1) from(" + sql + where + ")t ", Long.class, args.toArray());
        if (count == null || count == 0) {
            return ResultBean.getResultMap();
        }
        where.append(" limit ? offset ? ");
        args.add(pageSize);
        args.add(pageSize * pageNumber);
        List<BroadcastPlanVo> devices = jdbcTemplate.query(sql + where, new BeanPropertyRowMapper<>(BroadcastPlanVo.class), args.toArray());
        return ResultBean.getResultMap(Objects.requireNonNull(count).intValue(), devices);
    }

    @DevicePermission(key = "deviceIds")
    public ResultBean<Boolean> addOrUpdate(BroadcastPlanAddDto vo, String accessToken) {
        TokenRedisVo tokenVo = redisService.findTokenVo(accessToken);


        if (tokenVo == null) {
            return ResultBean.failedResultWithMsg("用户不存在");
        }
        if (vo == null) {
            return ResultBean.failedResultWithMsg("数据不能为空");
        }
        if (StringUtils.isBlank(vo.getStartTime())) {
            return ResultBean.failedResultWithMsg("播报开始时段不能为空");
        }
        if (StringUtils.isBlank(vo.getEndTime())) {
            return ResultBean.failedResultWithMsg("播报结束时段不能为空");
        }
        if (vo.getShopId() == null) {
            return ResultBean.failedResultWithMsg("门店不能为空");
        }
        if (CollUtil.isEmpty(vo.getDeviceIds())) {
            return ResultBean.failedResultWithMsg("关联设备不能为空");
        }

        if (vo.getType() == null) {
            return ResultBean.failedResultWithMsg("播报类型不能为空");
        }
        if (vo.getType().equals("2")) { // 自定义日期
            if (null == vo.getStartDate() || null == vo.getEndDate()) {
                return ResultBean.failedResultWithMsg("开始日期和结束日期不能为空");
            }
        }
        if (vo.getIntervalTime() == null) {
            return ResultBean.failedResultWithMsg("播放间隔时间不能为空");
        }


        Date date = new Date();
        if (vo.getId() == null) {
            //新增
            BroadcastPlan broadcastPlan = new BroadcastPlan();
            broadcastPlan.setStartTime(vo.getStartTime());
            broadcastPlan.setEndTime(vo.getEndTime());
            broadcastPlan.setShopId(vo.getShopId());
            broadcastPlan.setDeviceIds(vo.getDevIds());
            broadcastPlan.setCreateTime(date);
            broadcastPlan.setCreateUserId(tokenVo.getId());
            broadcastPlan.setStartDate(vo.getStartDate());
            broadcastPlan.setEndDate(vo.getEndDate());
            broadcastPlan.setType(vo.getType());
            broadcastPlan.setIntervalTime(vo.getIntervalTime());
            BroadcastPlan save = broadcastPlanRepository.save(broadcastPlan);
            vo.setId(save.getId());
        } else {
            Optional<BroadcastPlan> byId = broadcastPlanRepository.findById(vo.getId());
            if (byId.isPresent()) {
                //修改
                BroadcastPlan broadcastPlan = byId.get();
                broadcastPlan.setStartTime(vo.getStartTime());
                broadcastPlan.setEndTime(vo.getEndTime());
                broadcastPlan.setShopId(vo.getShopId());
                broadcastPlan.setDeviceIds(vo.getDevIds());
                broadcastPlan.setStartDate(vo.getStartDate());
                broadcastPlan.setEndDate(vo.getEndDate());
                broadcastPlan.setType(vo.getType());
                broadcastPlan.setIntervalTime(vo.getIntervalTime());
                broadcastPlanRepository.save(broadcastPlan);
            }
        }

        broadcastPlanVoiceWorkRepository.deleteByPlanId(vo.getId());
        List<Long> voiceWorkList = vo.getVoiceWorkList();
        if (!voiceWorkList.isEmpty()) {
            List<BroadcastPlanVoiceWorkRef> refList = new ArrayList<>();
            int sort = 0;
            for (Long voiceWorkId : voiceWorkList) {
                BroadcastPlanVoiceWorkRef ref = new BroadcastPlanVoiceWorkRef();
                ref.setPlanId(vo.getId());
                ref.setVoiceWorkId(voiceWorkId);
                ref.setSort(++sort);
                refList.add(ref);
            }
            broadcastPlanVoiceWorkRepository.saveAll(refList);
        }
        return ResultBean.successfulResult(true);
    }

    @BroadPlanPermission(key = "id")
    public ResultBean<Boolean> delete(Long id) {
        broadcastPlanRepository.deleteById(id);
        broadcastPlanVoiceWorkRepository.deleteByPlanId(id);
        return ResultBean.successfulResult(true);
    }

}
