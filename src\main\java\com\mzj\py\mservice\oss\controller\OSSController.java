package com.mzj.py.mservice.oss.controller;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.mservice.oss.service.OSSService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 阿里云OSS对象存储
 *
 * <AUTHOR>
 * @createDate: 2021年7月23日 下午2:08:20
 * @version: 1.0
 *
 */
@Controller
@RequestMapping("/oss")
public class OSSController {
	@Autowired
	private OSSService OSSService;

	/**
	 * 文件上传
	 *
	 * @param file
	 * @param bucketName
	 * @param type
	 *
	 * @return
	 * <AUTHOR>
	 * @date 2021年7月23日
	 */
	@PostMapping(value = "/file/{type}")
	@ResponseBody
	public ResultBean putFile(@RequestParam("file") MultipartFile file, String bucketName, @PathVariable String type) {
		String url = OSSService.putFile(bucketName, file, type);
		return ResultBean.successfulResult(url);
	}

	/**
	 * 文件删除
	 *
	 * @param fileKey
	 * @param bucketName
	 * @retur
	 * <AUTHOR>
	 * @date 2021年7月23日
	 */
	@DeleteMapping(value = "/file")
	@ResponseBody
	public ResultBean deleteObject(@RequestParam(required = true) String fileKey,
			@RequestParam(required = false) String bucketName) {
		return OSSService.deleteObject(bucketName, fileKey);
	}

	/**
	 * 临时批量上传某目录文件到oss公共读bucket
	 *
	 * @param dirPath
	 * @return
	 * <AUTHOR>
	 * @date 2021年3月24日
	 */
	@PostMapping(value = "/dir/temp")
	@ResponseBody
	public ResultBean<Object> putPublicFile(@RequestParam(required = false) String dirPath) {
		String dirUrl = "C:\\Users\\<USER>\\Desktop\\卡通头像/";
		if (StringUtils.isNotBlank(dirPath)) {
			dirUrl = dirPath;
		}
		File dir = new File(dirUrl);
		List<File> list = OSSService.getAllFile(dir, new ArrayList<>());

		Map<String, String> paths = new HashMap<>();
		for (File file2 : list) {
			String path = OSSService.putFile(null, file2, "avatar");
			paths.put(file2.getName(), "https://c.darhoo.com/" + path);
		}
		return ResultBean.successfulResult(paths);
	}
}
