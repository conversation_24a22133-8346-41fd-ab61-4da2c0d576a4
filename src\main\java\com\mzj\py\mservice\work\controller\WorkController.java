package com.mzj.py.mservice.work.controller;

import com.mzj.py.commons.DateUtils;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.mservice.common.ApiBaseController;
import com.mzj.py.mservice.oss.service.OSSService;
import com.mzj.py.mservice.work.service.WorkService;
import com.mzj.py.mservice.work.vo.WorkInfoVo;
import com.mzj.py.mservice.work.vo.WorkPageParam;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.util.Date;
import java.util.Map;

/**
 * 作品
 */
@RestController
@RequestMapping("work")
public class WorkController extends ApiBaseController {
    @Resource
    private WorkService workService;

    @Resource
    private OSSService ossService;

    /**
     * 作品列表查询
     */
    @GetMapping("list")
    public ResultBean<Map<String, Object>> list(@RequestHeader String accessToken,
            @RequestParam(name = "pageNumber", defaultValue = "0") Integer pageNumber,
            @RequestParam(name = "keyword", required = false) String keyword,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        return workService.list(accessToken, pageNumber, pageSize, keyword);
    }

    /**
     * 作品删除
     */
    @DeleteMapping("/{id}")
    public ResultBean<Object> delById(@PathVariable Long id) throws CustomException {
        workService.delById(id);
        return ResultBean.successfulResult(null);
    }

    /**
     * 编辑查询数据
     */
    @GetMapping("info")
    public ResultBean<WorkInfoVo> info(Long id) {
        return workService.info(id);
    }

    /**
     * 作品保存
     */
    @PostMapping("save")
    public ResultBean<Map<String, Object>> save(@RequestHeader String accessToken, @RequestBody WorkInfoVo vo)
            throws CustomException {
        Long shopId = getShopId(accessToken);
        if (shopId == null) {
            return ResultBean.getResultMap();
        }
        vo.setShopId(shopId);
        TokenRedisVo user = super.getUser(accessToken);
        vo.setUserId(user.getId());
        workService.save(vo);
        return ResultBean.successfulResult(null);
    }

    /**
     * 导出
     */
    @GetMapping("input")
    public void input(String url, HttpServletResponse response) {
        File file = ossService.getObjectFile(null, url);

        // 若文件不存在或不是有效文件，直接返回，避免后续空指针异常
        if (file == null || !file.exists() || !file.isFile()) {
            return;
        }

        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        String fileName = DateUtils.datetimeToString(new Date(), "yyyyMMddHHmmssSSS") + ".mp3";

        try (FileInputStream in = new FileInputStream(file)) {
            response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
            FileCopyUtils.copy(in, response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取用户可用音频
     *
     * @param accessToken
     * @return
     */
    @PostMapping(value = "/getWorkList")
    @ResponseBody
    public ResultBean<Map<String, Object>> getWorkList(@RequestHeader String accessToken,
            @RequestBody WorkPageParam param) {
        return workService.getWorkList(super.getShopId(accessToken), param);
    }
}
