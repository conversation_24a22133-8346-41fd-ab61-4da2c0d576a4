package com.mzj.py.aop;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.enums.StoreUserTypeEnum;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.shop.entity.ShopUserRef;
import com.mzj.py.mservice.shop.repository.ShopUserRefRepository;
import com.mzj.py.mservice.shop.repository.ShopRepository;
import com.mzj.py.mservice.shop.entity.Shop;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.*;

/**
 * 基于 {@link StorePermission} 的门店访问控制切面。
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class StorePermissionAspect {

    @Resource
    private RedisService redisService;
    @Resource
    private ShopUserRefRepository shopUserRefRepository;
    @Resource
    private ShopRepository shopRepository;

    @Pointcut("@annotation(StorePermission)")
    public void permissionPointCut() {
    }

    @Around("permissionPointCut() && @annotation(storePermission)")
    public Object around(ProceedingJoinPoint joinPoint, StorePermission storePermission) throws Throwable {
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attrs == null) {
            return joinPoint.proceed();
        }
        HttpServletRequest request = attrs.getRequest();
        String accessToken = request.getHeader("accessToken");
        if (StringUtils.isBlank(accessToken)) {
            log.warn("accessToken missing in request, uri={}", request.getRequestURI());
            return ResultBean.failedResultOfToken();
        }
        TokenRedisVo userVo = redisService.findTokenVo(accessToken);
        if (userVo == null) {
            log.warn("token invalid or expired, token={}", accessToken);
            return ResultBean.failedResultOfToken();
        }
        Long userId = userVo.getId();

        Set<Long> shopIds = new HashSet<>();
        MethodSignature methodSig = (MethodSignature) joinPoint.getSignature();
        String[] paramNames = methodSig.getParameterNames();
        Object[] args = joinPoint.getArgs();
        String idParam = storePermission.key();
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            if (arg == null) {
                continue;
            }

            String paramName = (paramNames != null && i < paramNames.length) ? paramNames[i] : "";

            boolean paramExplicitMatch = StringUtils.isNotBlank(idParam) && idParam.equals(paramName);
            if (!paramExplicitMatch) {
                continue;
            }

            // 2. 直接是 Long 主键
            if (arg instanceof Long) {
                shopIds.add((Long) arg);
                continue;
            }

            // 可能是 List<Long>
            if (arg instanceof Collection) {
                for (Object o : (Collection<?>) arg) {
                    if (o instanceof Long) {
                        shopIds.add((Long) o);
                    }
                }
            }
        }
        if (shopIds.isEmpty()) {
            return joinPoint.proceed(); // 无主键可校验，直接放行
        }
        for (Long shopId : shopIds) {
            ShopUserRef ref = shopUserRefRepository.findByShopIdAndUserId(shopId, userId);
            if (ref == null) {
                // 若用户未绑定当前门店，则尝试判断是否绑定了当前门店的父门店
                Shop shop = shopRepository.findById(shopId).orElse(null);
                if (shop != null && shop.getParentId() != null) {
                    ref = shopUserRefRepository.findByShopIdAndUserId(shop.getParentId(), userId);
                }
            }
            if (ref == null) {
                return ResultBean.failedResultWithMsg("用户未绑定该门店或其父门店，或无权限");
            }
            if (storePermission.operate() && !Objects.equals(ref.getRole(), StoreUserTypeEnum.ADMIN.getCode())) {
                return ResultBean.failedResultWithMsg("权限不足，仅管理员可操作");
            }
        }
        return joinPoint.proceed();
    }
}