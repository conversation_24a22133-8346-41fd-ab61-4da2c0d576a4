package com.mzj.py.mservice.installationPackage.controller;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.mservice.installationPackage.service.InstallationPackageService;
import com.mzj.py.mservice.installationPackage.vo.installationPackage.dto.InstallationPackageAddDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.websocket.server.PathParam;

/**
 * 安装包管理
 */
@Controller
@RequestMapping("/mini/installationPackage")
public class InstallationPackageController {

    @Autowired
    private InstallationPackageService installationPackageService;

    /**
     * 安装包列表
     * @param accessToken
     * @param pageSize
     * @param pageNumber
     * @return
     */
    @GetMapping
    @ResponseBody
    public ResultBean list(@RequestHeader String accessToken, @RequestParam(name = "pageSize",defaultValue = "10") Integer pageSize,
                           @RequestParam(name = "pageNumber",defaultValue = "0") Integer pageNumber) {
        return installationPackageService.list(accessToken, pageSize, pageNumber);
    }

    /**
     * 安装包添加或修改
     * @param accessToken
     * @param vo
     * @return
     */
    @PostMapping
    @ResponseBody
    public ResultBean addOrUpdate(@RequestHeader String accessToken,@RequestBody InstallationPackageAddDto vo){
        return installationPackageService.addOrUpdate(vo,accessToken);
    }

    /**
     * 删除安装包
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @ResponseBody
    public ResultBean delete(@PathVariable Long id){
        return installationPackageService.delete(id);
    }






}
