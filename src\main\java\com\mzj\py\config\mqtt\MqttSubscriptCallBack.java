package com.mzj.py.config.mqtt;

import com.alibaba.fastjson.JSONObject;
import com.mzj.py.mservice.deviceOperationLog.service.DeviceOperationLogService;
import com.mzj.py.mservice.pay.util.StringUtils;
import com.mzj.py.commons.RedisKeyConstant;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MqttSubscriptCallBack implements MqttCallback {

    @Value("${mqtt.clientId}")
    private String clientId;
    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;
    @Autowired
    private DeviceOperationLogService deviceOperationLogService;
    @Autowired
    private MqttSubscriptConfig mqttSubscriptConfig;

    /**
     * 与服务器断开的回调
     */
    @Override
    public void connectionLost(Throwable cause) {
        log.error(clientId + "client 与服务器断开连接！！异常类型: {}, 异常消息: {}",
                cause.getClass().getSimpleName(), cause.getMessage());
        log.error("连接断开堆栈信息: ", cause);
        log.info("连接断开，等待 SDK 自动重连，无需手动创建新连接");
    }

    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {

        String payload = new String(message.getPayload());
        JSONObject jsonObject = JSONObject.parseObject(payload);
        String timeStamp = jsonObject.getObject("timeStamp", String.class);

        if (StringUtils.isEmpty(timeStamp)) {
            log.error("MQTT返回的消息中没有 timeStamp");
            return;
        }

        log.info(String.format("response 接收消息主题 : %s", topic));
        log.info(String.format("response 接收消息Qos : %d", message.getQos()));
        log.info(String.format("response 接收消息内容 : %s", payload));
        log.info(String.format("response 接收消息retained : %b", message.isRetained()));
        int lastIndex = topic.lastIndexOf("/");
        String sn = topic.substring(lastIndex + 1);

        String lockKey = String.format(RedisKeyConstant.DEVICE_LOCK, sn);
        String snVal = redisTemplate.opsForValue().get(lockKey);

        if (StringUtils.isNotEmpty(snVal) && snVal.equals(timeStamp)) {
            // 将真正的响应写入单独的 key, 便于多并发场景下的区分
            String respKey = String.format(RedisKeyConstant.DEVICE_RESP, sn, timeStamp);
            redisTemplate.delete(lockKey);
            redisTemplate.opsForValue().set(respKey, payload, 300, TimeUnit.SECONDS);
            log.info(String.format("response 在redis中找到sn = %s, timeStamp= %s 的发送数据， value = %s", sn, snVal, payload));
            // 新增: 根据 sn 和 timeStamp 获取操作日志ID并更新日志
            String opLogKey = String.format(RedisKeyConstant.DEVICE_OP_LOG, sn, timeStamp);
            String opLogIdStr = redisTemplate.opsForValue().get(opLogKey);
            if (StringUtils.isNotEmpty(opLogIdStr)) {
                try {
                    deviceOperationLogService.update(Long.valueOf(opLogIdStr), payload);
                } catch (Exception e) {
                    log.error("更新设备操作日志失败 id={}, payload={}", opLogIdStr, payload, e);
                }
                redisTemplate.delete(opLogKey);
            }
        }
    }

    /**
     * 消息发布成功的回调
     */
    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
        IMqttAsyncClient client = token.getClient();
        log.info(client.getClientId() + "发布消息成功！");

    }
}
