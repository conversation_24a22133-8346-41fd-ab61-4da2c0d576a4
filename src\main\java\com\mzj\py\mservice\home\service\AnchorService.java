package com.mzj.py.mservice.home.service;

import com.alibaba.fastjson.JSON;
import com.mzj.py.commons.*;
import com.mzj.py.commons.enums.DubbingTypeEnum;
import com.mzj.py.commons.enums.VocieTransforTypeEnum;
import com.mzj.py.commons.exception.BizException;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.mservice.compound.compound.DubbingFactory;
import com.mzj.py.mservice.compound.compound.SpeechSynthesizerRequest;
import com.mzj.py.mservice.compound.entity.SpeechSynthesizerDto;
import com.mzj.py.mservice.compound.utils.FfmpegUtil;
import com.mzj.py.mservice.compound.utils.FileRenameUtils;
import com.mzj.py.mservice.compound.utils.FileUtil;
import com.mzj.py.mservice.compound.utils.LineInsertUtils;
import com.mzj.py.mservice.home.controller.request.RecordUploadReq;
import com.mzj.py.mservice.home.entity.*;
import com.mzj.py.mservice.home.repository.*;
import com.mzj.py.mservice.home.vo.*;
import com.mzj.py.mservice.oss.service.OSSService;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.wxuser.entity.WxUser;
import com.mzj.py.mservice.wxuser.repository.WxUserRepository;
import org.apache.commons.collections.CollectionUtils;
import org.jaudiotagger.audio.AudioFileIO;
import org.jaudiotagger.audio.mp3.MP3AudioHeader;
import org.jaudiotagger.audio.mp3.MP3File;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("ResultOfMethodCallIgnored")
@Service
public class AnchorService {
    private final Logger LOG = LoggerFactory.getLogger(AnchorService.class);
    @Value("${ali.oss.downFile.tempdir}")
    private String appUrl;
    @Value("${ali.oss.bucket.url}")
    private String ossUrl;
    @Resource
    private RedisService redisService;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private DubAnchorRepository anchorRepository;

    @Resource
    private DubTemplateTypeRepository templateTypeRepository;

    @Resource
    private DubBackgroundMusicTypeRepository musicTypeRepository;

    @Resource
    private VoiceWorkRepository workRepository;

    @Resource
    private VoicePacketRepository packetRepository;

    @Resource
    private OSSService ossService;

    @Resource
    private DubAnchorRepository dubAnchorRepository;

    @Resource
    private DubbingFactory dubbingFactory;

    @Resource
    private WxUserRepository wxUserRepository;

    @Resource
    private LongAnchorRepository longAnchorRepository;

    @Resource
    private UserRecordRepository userRecordRepository;

    @Resource
    private AiStyleRepository aiStyleRepository;
    @Resource
    private AiClassRepository aiClassRepository;
    @Resource
    private AiLanguageRepository aiLanguageRepository;
    @Resource
    private UserBackgroundMusicRepository userBackgroundMusicRepository;

    public ResultBean<List<AnchorVo>> list(Long id) {
        StringBuilder sql = new StringBuilder(
                "select id,name,usage_scenario,type_name,url,voice_url,archor_tag from dub_anchor WHERE NAME IN ('促销男声','知米','促销女声','亚群')");
        sql.append(" limit 0, 4");
        List<AnchorVo> list = jdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(AnchorVo.class));
        return ResultBean.successfulResult(list);
    }

    public ResultBean<List<AnchorVo>> longList(Long id) {
        StringBuilder sql = new StringBuilder(
                "select id,name,usage_scenario,type_name,url,voice_url from long_anchor WHERE NAME IN ('智逍遥','智瑜','智聆','智美','智云')");
        sql.append(" limit 0, 4");
        List<AnchorVo> list = jdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(AnchorVo.class));
        for (AnchorVo vo : list) {
            switch (Integer.parseInt(vo.getId() + "")) {
                case 1:
                    vo.setRemark("标准女声");
                    break;
                case 2:
                    vo.setRemark("标准女声");
                    break;
                case 3:
                    vo.setRemark("激昂解说");
                    break;
                case 4:
                    vo.setRemark("标准男声");
                    break;
                case 5:
                    vo.setRemark("标准女声");
                    break;
                default:
                    break;
            }
        }
        return ResultBean.successfulResult(list);
    }

    public ResultBean<List<Map<String, String>>> typeList() {
        List<String> strs = anchorRepository.getDistinctByUsageScenario();
        List<Map<String, String>> vos = strs.stream().map(s -> {
            Map<String, String> vo = new HashMap<>();
            vo.put("usageScenario", s);
            return vo;
        }).collect(Collectors.toList());
        return ResultBean.successfulResult(vos);
    }

    public ResultBean<Map<String, Object>> anchorByTypeName(String accessToken, String name, Integer pageNumber,
                                                            Integer pageSize) {
        TokenRedisVo vo = getUser(accessToken).getResultData();
        List<Object> args = new ArrayList<>();
        args.add(vo.getId());
        args.add(name);
        StringBuilder sql = new StringBuilder("select da.id,da.name,da.usage_scenario,da.type_name,da.url," +
                "da.voice_url,da.archor_tag, CASE WHEN dfa.anchor_id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited " +
                "from dub_anchor da left join dub_follow_anchor dfa on da.id= dfa.anchor_id and dfa.user_id =? where da.usage_scenario = ? order by da.order_index desc");
        Map<String, Object> map = jdbcTemplate.queryForMap("select count(1) as total from (" + sql + ") s",
                args.toArray());
        sql.append(" limit ? offset ? ");
        args.add(pageSize);
        args.add(pageSize * pageNumber);
        List<AnchorVo> list = jdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(AnchorVo.class),
                args.toArray());
        map.put("result", list);
        return ResultBean.successfulResult(map);
    }

    public ResultBean<Map<String, Object>> longAchor(String accessToken, String name, Integer pageNumber,
                                                     Integer pageSize) {
        TokenRedisVo vo = getUser(accessToken).getResultData();
        List<Object> args = new ArrayList<>();
        args.add(vo.getId());
        args.add(name);
        StringBuilder sql = new StringBuilder("select l.id,l.name,l.usage_scenario,l.type_name,l.url,l.voice_url," +
                "CASE WHEN lf.anchor_id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited" +
                " from long_anchor l left join long_follow_anchor lf on" +
                " l.id= lf.anchor_id and lf.user_id =? " +
                " where usage_scenario = ?");
        Map<String, Object> map = jdbcTemplate.queryForMap("select count(1) as total from (" + sql + ") s",
                args.toArray());
        sql.append(" limit ? offset ? ");
        args.add(pageSize);
        args.add(pageSize * pageNumber);
        List<AnchorVo> list = jdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(AnchorVo.class),
                args.toArray());
        map.put("result", list);
        return ResultBean.successfulResult(map);
    }

    public ResultBean<List<DubTemplateType>> templateType() {
        return ResultBean.successfulResult(templateTypeRepository.findAllByDelStatus(0));
    }

    public ResultBean<Map<String, Object>> templateByType(Long id, Integer pageNumber, Integer pageSize) {
        List<Object> args = new ArrayList<>();
        args.add(id);
        StringBuilder sql = new StringBuilder("select * from dub_template where template_type_id = ?")
                .append(" and del_status = 0 order by create_time desc ");
        Map<String, Object> map = jdbcTemplate.queryForMap("select count(1) as total from (" + sql + ") s ",
                args.toArray());
        sql.append(" limit ? offset ? ");
        args.add(pageSize);
        args.add(pageSize * pageNumber);
        List<TemplateVo> list = jdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(TemplateVo.class),
                args.toArray());
        map.put("result", list);
        return ResultBean.successfulResult(map);
    }

    public ResultBean<List<DubBackgroundMusicType>> musicType() {
        return ResultBean.successfulResult(musicTypeRepository.findAll());
    }

    public ResultBean<Map<String, Object>> musicList(String accessToken, Long id, Integer pageNumber,
                                                     Integer pageSize) {
        TokenRedisVo vo = getUser(accessToken).getResultData();
        List<Object> args = new ArrayList<>();
        args.add(vo.getId());
        args.add(id);
        StringBuilder sql = new StringBuilder("select m.*,m.music_url as url, " +
                "CASE WHEN uf.bgm_id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited " +
                "from dub_background_music m left join user_follow_bgm uf on m.id=uf.bgm_id and uf.user_id = ? " +
                "where m.type_id = ? and m.del_status = 0 order by m.create_time desc");
        Map<String, Object> map = jdbcTemplate.queryForMap("select count(1) as total from (" + sql + ") s",
                args.toArray());
        sql.append(" limit ? offset ? ");
        args.add(pageSize);
        args.add(pageSize * pageNumber);
        List<MusicVo> list = jdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(MusicVo.class),
                args.toArray());
        map.put("result", list);
        return ResultBean.successfulResult(map);
    }

    public ResultBean<Map<String, Object>> myMusic(String accessToken) {
        Long userId = getUser(accessToken).getResultData().getId();
        List<UserBackgroundMusic> userBackgroundMusicList = userBackgroundMusicRepository.findByUserId(userId);
        List<UserBackgroundMusicVo> userBackgroundMusicVos = userBackgroundMusicList.stream().map(obj -> {
            UserBackgroundMusicVo userBackgroundMusicVo = new UserBackgroundMusicVo();
            BeanUtils.copyProperties(obj, userBackgroundMusicVo);
            userBackgroundMusicVo.setUrl(obj.getMusicUrl());
            return userBackgroundMusicVo;
        }).collect(Collectors.toList());
        Map<String, Object> map = new HashMap<>(1);
        map.put("result", userBackgroundMusicVos);
        return ResultBean.successfulResult(map);
    }

    public ResultBean<Object> delMyMusic(String accessToken, Long musicId) {
        Long userId = getUser(accessToken).getResultData().getId();
        userBackgroundMusicRepository.deleteByUserIdAndId(userId, musicId);
        return ResultBean.successfulResult(1);
    }

    public ResultBean<Map<String, Object>> audition(SpeechSynthesizerDto dto) {
        Long userId = dto.getUserId();
        LOG.info("Anchor dto={}", JSON.toJSONString(dto));
        ResultBean<Map<String, Object>> resultBean = new ResultBean<>();
        if (dto.getText().isEmpty() || dto.getText() == null) {
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg("请填写需合成录音文本");
            return resultBean;
        }
        if (dto.getText().length() > 300) {
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg("字数不能超过300字");
            return resultBean;
        }
        DubAnchor anchor = dubAnchorRepository.findById(dto.getAnchorId()).orElse(null);
        if (anchor == null) {
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg("请选择需合成录音主播");
            return resultBean;
        }
        String beforeDto = redisService.getvoiceRepeat(userId);
        Map<String, Object> map = new HashMap<>();
        dto.setVoice(anchor.getVoiceName());
        dto.setIsEmotion(anchor.getIsEmotion());
        dto.setEmotion(anchor.getEmotion());
        if (dto.getBgm() != null && !dto.getBgm().contains(ossUrl)) {
            dto.setBgm(ossUrl + dto.getBgm());
        }
        LOG.info("bgm={}", dto.getBgm());
        SpeechSynthesizerDto afterDto = new SpeechSynthesizerDto();
        BeanUtils.copyProperties(dto, afterDto);
        if (StringUtils.isNotBlank(beforeDto)) {
            LOG.info("beforeDto={},dto={}", beforeDto, dto);
            if (JSON.toJSONString(dto).equals(beforeDto)) {
                resultBean.setCode(StatusCode.ERROR_CODE_20010.getErrorCode());
                resultBean.setMsg(StatusCode.ERROR_CODE_20010.getErrorMsg());
                return resultBean;
            }
        }
        Integer number = redisService.getVoiceNumber(dto.getUserId());
        String text = dto.getText().replaceAll("\\s*", "");
        text = text.replace("\u00A0", "");
        String voiceName = FileRenameUtils.createVoiceName(text, number);
        redisService.putVoiceNumber(number, dto.getUserId());
        dto.setUrl(appUrl);
        dto.setType(anchor.getType());
        dto.setText(FileUtil.replacePauseTags(dto.getText()));
        LOG.info("转换后的text={}", dto.getText());
        String url = null;
        try {
            url = dubbingFactory.process(dto, redisService);
        } catch (Exception e) {
            LOG.error("合成错误{}", e);
            redisService.setvoiceRepeat(userId, "", 20000L);
            if (e instanceof BizException) {
                BizException bizException = (BizException) e;
                return ResultBean.failedResultWithMsg(bizException.getMsg());
            }
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg("合成失败, 请更换主播尝试");
            return resultBean;
        }
        File file = new File(url);
        Integer voiceTime = null;
        File targetFile = FileUtil.createFileIfNotExists(appUrl + "compound" + File.separator + file.getName());
        try {
            FfmpegUtil.voloumVipAudio(file, targetFile, 5);
            MP3File f = (MP3File) AudioFileIO.read(targetFile);
            MP3AudioHeader audioHeader = (MP3AudioHeader) f.getAudioHeader();
            voiceTime = audioHeader.getTrackLength();
            url = ossService.putFileOld(null, targetFile, "temp/" + userId);
            targetFile.delete();
        } catch (Exception e) {
            redisService.setvoiceRepeat(userId, "", 20000L);
            LOG.error(e.getMessage(), e);
            targetFile.delete();
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg("合成失败, 请更换主播尝试");
            return resultBean;
        }
        map.put("url", url);
        map.put("time", voiceTime);
        map.put("voiceUrlName", voiceName);
        return ResultBean.successfulResult(map);
    }

    public ResultBean<TokenRedisVo> getUser(String accessToken) {
        TokenRedisVo vo = redisService.findTokenVo(accessToken);
        if (vo == null) {
            return ResultBean.failedResultOfToken();
        }
        return ResultBean.successfulResult(vo);
    }

    public ResultBean<Map<String, Object>> save(VoiceWorkVo dto, String accessToken) throws CustomException {
        if (StringUtils.isEmpty(dto.getUrl())) {
            throw new CustomException("音频文件不能为空");
        }
        TokenRedisVo vo = getUser(accessToken).getResultData();
        File file = ossService.getObjectFile(null, dto.getUrl());
        int preIndex = dto.getUrl().lastIndexOf("/");
        int lastIndex = dto.getUrl().lastIndexOf(".");
        String voiceName = dto.getUrl().substring(preIndex + 1, lastIndex);
        String url = ossService.putFileToName(null, file, "voice", vo.getId() + "/" + voiceName);
        VoiceWork work = new VoiceWork();
        work.setAnchorId(dto.getAnchorId());
        if (dto.getBackgroundMusicId() != null) {
            work.setBackgroundMusicId(dto.getBackgroundMusicId());
        }
        if (dto.getBackgroundMusicVolume() != null) {
            work.setBackgroundMusicVolume(dto.getBackgroundMusicVolume());
        }
        work.setContent(dto.getText());
        work.setCreateTime(new Date());
        work.setDelStatus(0);
        if (dto.getPitch() != null) {
            work.setPitch(dto.getPitch());
        } else {
            work.setPitch(0);
        }
        String sampleRate = redisService.getValue(RedisKeyConstant.SAMPLE_RATE);
        if (sampleRate != null) {
            if (sampleRate.equals("8000") || sampleRate.equals("16000") || sampleRate.equals("18000")) {
                work.setSampleRate(Integer.parseInt(sampleRate));
            }
        } else {
            work.setSampleRate(16000);
        }
        if (!dto.getTitle().equals("") && dto.getTitle() != null) {
            work.setTitle(dto.getTitle());
        } else {
            Integer count = jdbcTemplate.queryForObject(
                    "select count(1) from dub_voice_work where user_id = ? and title like concat('%','作品','%')",
                    Integer.class, vo.getId());
            if (count != null) {
                work.setTitle("作品" + (count + 1));
            } else {
                work.setTitle("作品1");
            }
        }
        work.setSpeed(0);
        if (dto.getSpeed() != null) {
            work.setSpeed(dto.getSpeed());
        }
        work.setUserId(vo.getId());
        work.setVolume(0);
        if (dto.getVolume() != null) {
            work.setVolume(dto.getVolume());
        }
        work.setVoiceTime(dto.getVoiceTime());
        try {
            work = workRepository.save(work);
        } catch (Exception e) {
            ResultBean<Map<String, Object>> resultBean = new ResultBean<>();
            resultBean.setCode(StatusCode.ERROR_CODE_20009.getErrorCode());
            resultBean.setMsg(StatusCode.ERROR_CODE_20009.getErrorMsg());
            return resultBean;
        }
        VoicePacket packet = new VoicePacket();
        packet.setFileUrl(url);
        packet.setName(work.getTitle());
        packet.setVoiceTime(dto.getVoiceTime());
        packet.setVoiceWorkId(work.getId());
        packet = packetRepository.save(packet);
        work.setVoiceId(packet.getId());
        workRepository.save(work);
        Map<String, Object> map = new HashMap<>();
        map.put("url", url);
        map.put("id", work.getId());
        file.delete();
        return ResultBean.successfulResult(map);
    }

    public ResultBean<Map<String, Object>> longCompound(SpeechSynthesizerDto dto) {
        LOG.info("Anchor dto={}", JSON.toJSONString(dto));
        ResultBean<Map<String, Object>> resultBean = new ResultBean<>();
        Map<String, Object> map = new HashMap<>();
        Long userId = dto.getUserId();
        String beforeDto = redisService.getvoiceRepeat(userId);
        LongAnchor anchor = longAnchorRepository.findById(dto.getAnchorId()).orElse(null);
        if (anchor == null) {
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg("请选择需合成录音主播");
            return resultBean;
        }
        dto.setVoice(anchor.getVoiceName());
        if (dto.getText().length() > 2000) {
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg("字数不能超过2000字");
            return resultBean;
        }
        SpeechSynthesizerDto afterDto = new SpeechSynthesizerDto();
        BeanUtils.copyProperties(dto, afterDto);
        if (StringUtils.isNotBlank(beforeDto)) {
            if (JSON.toJSONString(dto).equals(beforeDto)) {
                resultBean.setCode(StatusCode.ERROR_CODE_20010.getErrorCode());
                resultBean.setMsg(StatusCode.ERROR_CODE_20010.getErrorMsg());
                return resultBean;
            }
        }
        if (dto.getText().equals("") || dto.getText() == null) {
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg("请填写需合成录音文本");
            return resultBean;
        }
        if (dto.getVoice().equals("") || dto.getVoice() == null) {
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg("请选择需合成录音主播");
            return resultBean;
        }
        if (dto.getBgm() != null && !dto.getBgm().contains(ossUrl)) {
            dto.setBgm(ossUrl + dto.getBgm());
        }
        LOG.info("bgm={}", dto.getBgm());
        Integer number = redisService.getVoiceNumber(dto.getUserId());
        String text = dto.getText().replaceAll("\\s*", "");
        text = text.replace("\u00A0", "");
        String voiceName = FileRenameUtils.createVoiceName(text, number);
        dto.setUrl(appUrl);
        dto.setType(anchor.getType());
        String url = null;
        try {
            url = dubbingFactory.process(dto, redisService);
        } catch (Exception e) {
            redisService.setvoiceRepeat(userId, "", 20000L);
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg("合成失败, 请更换主播尝试");
            return resultBean;
        }
        File file = new File(url);
        Integer voiceTime = null;
        try {
            MP3File f = (MP3File) AudioFileIO.read(file);
            MP3AudioHeader audioHeader = (MP3AudioHeader) f.getAudioHeader();
            voiceTime = audioHeader.getTrackLength();
            url = ossService.putFileOld(null, file, "temp/" + userId);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            if (e.getMessage().contains("No audio header found")) {
                file.delete();
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("合成失败, 请更换主播尝试");
                return resultBean;
            }
        }
        map.put("url", url);
        map.put("time", voiceTime);
        map.put("voiceUrlName", voiceName);
        LOG.info("合成语音的文件删除状态{}", file.delete());
        redisService.setvoiceRepeat(userId, JSON.toJSONString(afterDto), 20000L);
        return ResultBean.successfulResult(map);
    }

    public ResultBean<Map<String, Object>> dialog(SpeechSynthesizerDto dto, String accessToken) {
        LOG.info("Anchor dto={}", JSON.toJSONString(dto));
        ResultBean<Map<String, Object>> resultBean = new ResultBean<>();
        TokenRedisVo vo = getUser(accessToken).getResultData();
        Long userId = vo.getId();
        DubAnchor anchor = anchorRepository.findById(dto.getAnchorId()).get();
        dto.setVoice(anchor.getVoiceName());
        if (anchor.getType().equalsIgnoreCase(DubbingTypeEnum.DY_DUBBING.name())) {
            dto.setVolume(dto.getVolume() - 70);
        } else if (anchor.getType().equalsIgnoreCase(DubbingTypeEnum.TX_SHOT_DUBBING.name())) {
            dto.setVolume(dto.getVolume() - 30);
        }
        if (dto.getText().length() > 300) {
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg("字数不能超过300字");
            return resultBean;
        }
        Map<String, Object> map = new HashMap<>();
        if (dto.getText().equals("") || dto.getText() == null) {
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg("请填写需合成录音文本");
            return resultBean;
        }
        if (dto.getVoice().equals("") || dto.getVoice() == null) {
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg("请选择需合成录音主播");
            return resultBean;
        }
        if (dto.getBgm() != null && !dto.getBgm().contains(ossUrl)) {
            dto.setBgm(ossUrl + dto.getBgm());
        }
        Integer number = redisService.getVoiceNumber(dto.getUserId());
        String text = dto.getText().replaceAll("\\s*", "");
        text = text.replace("\u00A0", "");
        String voiceName = FileRenameUtils.createVoiceName(text, number);
        redisService.putVoiceNumber(number, dto.getUserId());
        LOG.info("bgm={}", dto.getBgm());
        dto.setUrl(appUrl);
        dto.setType(anchor.getType());
        dto.setText(FileUtil.replacePauseTags(dto.getText()));
        LOG.info("转换后的text={}", dto.getText());
        String url = null;
        try {
            url = dubbingFactory.process(dto, redisService);
        } catch (Exception e) {
            redisService.setvoiceRepeat(userId, "", 20000L);
            resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
            resultBean.setMsg("合成失败, 请更换主播尝试");
            return resultBean;
        }
        File file = new File(url);
        Integer voiceTime = null;
        try {
            MP3File f = (MP3File) AudioFileIO.read(file);
            MP3AudioHeader audioHeader = (MP3AudioHeader) f.getAudioHeader();
            voiceTime = audioHeader.getTrackLength();
            url = ossService.putFileOld(null, file, "temp/" + vo.getId());
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            if (e.getMessage().contains("No audio header found")) {
                file.delete();
                resultBean.setCode(StatusCode.ERROR_CODE_10001.getErrorCode());
                resultBean.setMsg("合成失败, 请更换主播尝试");
                return resultBean;
            }
        }
        map.put("url", url);
        map.put("time", voiceTime);
        map.put("voiceUrlName", voiceName);
        file.delete();
        return ResultBean.successfulResult(map);
    }

    public ResultBean<Map<String, Object>> uploadRecordFree(MultipartFile file, String accessToken,
                                                            RecordUploadReq uploadReq) {
        LOG.info("录音上传中");
        long size = file.getSize();
        if (size > 7 * 1024 * 1024) {
            return ResultBean.failedResultWithMsg("录音文件不能超过7M");
        }
        ResultBean<Map<String, Object>> resultBean = new ResultBean<>();
        Long userId = getUser(accessToken).getResultData().getId();
        if (uploadReq.getBgm() != null && !uploadReq.getBgm().contains(ossUrl)) {
            uploadReq.setBgm(ossUrl + uploadReq.getBgm());
        }
        String bgm = uploadReq.getBgm();
        Integer bugRate = uploadReq.getBugRate();
        Double volume = null;
        Double speechRate = null;
        Double pitchRate = null;
        if (uploadReq.getVolume() != null) {
            volume = LineInsertUtils.mapRange(uploadReq.getVolume(), -5,
                    5, -5, 20);
        }
        if (uploadReq.getPitchRate() != null) {
            // 语调，范围是-500~500，可选，默认是0
            pitchRate = LineInsertUtils.mapRange(uploadReq.getPitchRate(), -500,
                    500, 0.0, 2.0);
        }
        if (uploadReq.getSpeechRate() != null) {
            // 语速，范围是-500~500，默认是0
            speechRate = LineInsertUtils.mapRange(uploadReq.getSpeechRate(), 0.0,
                    2.0, 0.1, 2.0);
        }
        int index = uploadReq.getName().lastIndexOf(".");
        String fileName = uploadReq.getName().substring(0, index);
        String suffixName = uploadReq.getName().substring(index);
        fileName = fileName.replaceAll("\\s*", "");
        fileName = FileRenameUtils.cleanText(fileName);
        if (fileName.length() > 4) {
            fileName = fileName.substring(fileName.length() - 4, fileName.length());
        }
        File targetFile = new File(appUrl + fileName + ".mp3");
        try {
            File processFile = new File(appUrl + userId + "&" + fileName + suffixName);
            file.transferTo(processFile);
            File sourceFile = new File(appUrl + userId + "$" + fileName + ".mp3");
            FfmpegUtil.processAudio(processFile, sourceFile, speechRate, pitchRate, volume.intValue());
            if (StringUtils.isNotBlank((bgm))) {
                String bgmFileName = appUrl + "bgm" + fileName + ".wav";
                File bgmFile = FileUtil.taiseng(bgm, bgmFileName, bugRate);
                File outFile = new File(appUrl + "&&" + fileName + ".wav");
                FfmpegUtil.mixBgm(sourceFile, bgmFile, outFile, uploadReq.getBeforeDelay(), uploadReq.getAfterDelay(),
                        uploadReq.getBgmCenterVolum());
                FileUtil.coverToMp3Heigh(outFile, targetFile);
            } else {
                FileUtil.coverToMp3Heigh(sourceFile, targetFile);
            }
            MP3File f = (MP3File) AudioFileIO.read(targetFile);
            MP3AudioHeader audioHeader = (MP3AudioHeader) f.getAudioHeader();
            Integer voiceTime = audioHeader.getTrackLength();
            String url = ossService.putFileOld(null, targetFile, "temp/" + userId);
            Map<String, Object> map = new HashMap<>(2);
            map.put("url", url);
            map.put("time", voiceTime);
            List<UserRecord> userRecordList = userRecordRepository.findByUserIdAndArchorTypeOnToday(userId,
                    VocieTransforTypeEnum.FREE_TRANSFOR.name());
            if (userRecordList.size() == 0) {
                UserRecord userRecord = new UserRecord();
                userRecord.setUserId(userId);
                userRecord.setArchorType(VocieTransforTypeEnum.FREE_TRANSFOR.name());
                userRecord.setRecordTime(voiceTime - uploadReq.getBeforeDelay() - uploadReq.getAfterDelay() + 1);
                userRecordRepository.save(userRecord);
            } else {
                UserRecord userRecord = userRecordList.get(0);
                userRecord.setRecordTime(userRecord.getRecordTime() + voiceTime - uploadReq.getBeforeDelay()
                        - uploadReq.getAfterDelay() + 1);
                userRecordRepository.save(userRecord);
            }
            LOG.info("录音处理的文件删除状态{}", targetFile.delete());
            return ResultBean.successfulResult(map);
        } catch (Exception e) {
            LOG.info("上传文件异常{}，文件后缀名{}", e, suffixName);
            targetFile.delete();
            resultBean.setCode(StatusCode.ERROR_CODE_20008.getErrorCode());
            resultBean.setMsg(StatusCode.ERROR_CODE_20008.getErrorMsg());
            return resultBean;
        }
    }

    public ResultBean<List<AiClass>> aiClassList() {
        List<AiClass> aiClassList = aiClassRepository.findByDelStatus(0);
        return ResultBean.successfulResult(aiClassList);
    }

    public ResultBean<List<AiStyle>> aiStyleList() {
        List<AiStyle> aiStyleList = aiStyleRepository.findByDelStatus(0);
        return ResultBean.successfulResult(aiStyleList);
    }

    public ResultBean<List<AiLanguage>> aiLanguageList() {
        List<AiLanguage> aiLanguageList = aiLanguageRepository.findByDelStatus(0);
        return ResultBean.successfulResult(aiLanguageList);
    }

    public ResultBean<Map<String, Object>> uploadVideo(MultipartFile file, String accessToken, String name) {
        LOG.info("上传中");
        if (file.getSize() > 1024 * 1024 * 500) {
            return ResultBean.failedResultWithMsg("文件最大上传500M");
        }
        ResultBean<Map<String, Object>> resultBean = new ResultBean<>();
        Long userId = getUser(accessToken).getResultData().getId();
        try {
            int index = name.lastIndexOf(".");
            String fileName = name.substring(0, index);
            fileName = fileName.replaceAll("\\s*", "");
            fileName = FileRenameUtils.cleanText(fileName);
            if (fileName.length() > 4) {
                fileName = fileName.substring(0, 4);
            }
            File processFile = new File(appUrl + userId + "&" + name);
            File sourceFile = new File(appUrl + userId + "&" + fileName + ".wav");
            FileUtil.tempCoverToWav(file, sourceFile, processFile);
            File targetFile = new File(appUrl + userId + fileName + ".wav");
            FileUtil.trunFile(sourceFile, targetFile);
            String url = ossService.dijia(null, targetFile, "bgm", userId + "/" + fileName);
            sourceFile.delete();
            targetFile.delete();
            UserBackgroundMusic userBackgroundMusic = new UserBackgroundMusic();
            userBackgroundMusic.setName(fileName);
            userBackgroundMusic.setUserId(userId);
            userBackgroundMusic.setDelStatus(0);
            userBackgroundMusic.setCreateTime(new Date());
            userBackgroundMusic.setMusicUrl(url);
            userBackgroundMusicRepository.save(userBackgroundMusic);
            return ResultBean.successfulResult(null);
        } catch (Exception e) {
            LOG.info("上传文件异常{}", e);
            resultBean.setCode(StatusCode.ERROR_CODE_20008.getErrorCode());
            resultBean.setMsg(StatusCode.ERROR_CODE_20008.getErrorMsg());
            return resultBean;
        }
    }

    public ResultBean<Map<String, Object>> mergeAudio(MergeVo mergeVo, String accessToken) {
        if (mergeVo.getBgm() != null && !mergeVo.getBgm().contains(ossUrl)) {
            mergeVo.setBgm(ossUrl + mergeVo.getBgm());
        }
        TokenRedisVo vo = getUser(accessToken).getResultData();
        Map<String, Object> map = new HashMap<>();
        Integer number = redisService.getVoiceNumber(vo.getId());
        String text = mergeVo.getText();
        List<String> audioList = mergeVo.getAudio();
        String voiceName = FileRenameUtils.createVoiceName(text, number);
        redisService.putVoiceNumber(number, vo.getId());
        String tempFileName = appUrl + FileUtil.getFileNewName(".mp3");
        String outFileName = appUrl + FileUtil.getFileNewName(".mp3");
        String fileName = appUrl + FileUtil.getFileNewName(".mp3");
        File file = new File(fileName);
        try {
            if (!StringUtils.isEmpty(mergeVo.getBgm())) {
                String wavFileName = appUrl + FileUtil.getFileNewName(".wav");
                File outFile = combine(outFileName, audioList, tempFileName);
                File wavFile = new File(wavFileName);
                FileUtil.coverToWav(outFile, wavFile);
                String bgmFileName = appUrl + FileUtil.getFileNewName(".wav");
                File bgmFile = FileUtil.taiseng(mergeVo.getBgm(), bgmFileName, mergeVo.getBugRate());
                File resFile = new File(appUrl + FileUtil.getFileNewName(".wav"));
                FfmpegUtil.mixBgm(wavFile, bgmFile, resFile, mergeVo.getBeforeDelay(), mergeVo.getAfterDelay(),
                        mergeVo.getBgmCenterVolum());
                FileUtil.coverToMp3(resFile, file);
            } else {
                combine(fileName, audioList, tempFileName);
            }
            String url = ossService.putFileOld(null, file, "temp/" + vo.getId());
            file.delete();
            map.put("time", 0);
            map.put("url", url);
            map.put("voiceUrlName", voiceName);
            return ResultBean.successfulResult(map);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public File combine(String outFile, List<String> files, String tempFile) throws Exception {
        Path tempFilePath = Paths.get(outFile);
        FileOutputStream fos = new FileOutputStream(tempFilePath.toFile(), true);
        for (int i = 0; i < files.size(); i++) {
            File file = downloadFile(files.get(i), tempFile);
            FileInputStream fis = new FileInputStream(file);
            int len = 0;
            for (byte[] buf = new byte[1024 * 1024]; (len = fis.read(buf)) != -1; ) {
                fos.write(buf, 0, len);
            }
            fis.close();
            boolean delete = file.delete();
            LOG.info("对话配音删除合成的临时文件：{}", delete);
        }
        fos.close();
        return tempFilePath.toFile();
    }

    private File downloadFile(String fileURL, String voiceName) throws Exception {
        URL url = new URL(ossUrl + fileURL);
        HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
        int responseCode = httpConn.getResponseCode();

        // 检查HTTP响应代码
        if (responseCode == HttpURLConnection.HTTP_OK) {
            String tempFileName = voiceName; // 临时文件名
            Path tempFilePath = Paths.get(tempFileName);
            try (InputStream inputStream = new BufferedInputStream(httpConn.getInputStream());
                 FileOutputStream outputStream = new FileOutputStream(tempFilePath.toFile())) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            } finally {
                httpConn.disconnect();
            }

            return tempFilePath.toFile();
        } else {
            throw new Exception("GET request not worked: " + responseCode);
        }
    }


    public ResultBean<List<AnchorVo>> hotList(String accessToken) {
        TokenRedisVo vo = getUser(accessToken).getResultData();
        List<Object> args = new ArrayList<>();
        args.add(vo.getId());
        StringBuilder sql = new StringBuilder("select h.id,h.name,h.usage_scenario,h.type_name,h.url,h.voice_url,h.archor_tag, "
                + "CASE WHEN vf.anchor_id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited " +
                " from hot_anchor h left join follow_anchor vf on h.id= vf.anchor_id and vf.user_id =? ");
        List<AnchorVo> list = jdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(AnchorVo.class), args.toArray());
        return ResultBean.successfulResult(list);
    }
}
