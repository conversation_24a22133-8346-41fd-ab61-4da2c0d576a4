package com.mzj.py.mservice.shop.service;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.enums.StoreReviewEnum;
import com.mzj.py.commons.enums.StoreTypeEnum;
import com.mzj.py.commons.enums.StoreUserTypeEnum;
import com.mzj.py.mservice.common.PageResult;
import com.mzj.py.mservice.home.entity.Device;
import com.mzj.py.mservice.home.repository.DeviceRepository;
import com.mzj.py.mservice.shop.entity.Shop;
import com.mzj.py.mservice.shop.entity.ShopUserRef;
import com.mzj.py.mservice.shop.repository.ShopRepository;
import com.mzj.py.mservice.shop.repository.ShopUserRefRepository;
import com.mzj.py.mservice.shop.vo.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import org.mockito.ArgumentCaptor;

/**
 * StoreService集成测试类
 * 测试复杂业务场景和完整流程
 */
@ExtendWith(MockitoExtension.class)
class StoreServiceIntegrationTest {

        @InjectMocks
        private StoreService storeService;

        @Mock
        private JdbcTemplate jdbcTemplate;
        @Mock
        private ShopRepository shopRepository;
        @Mock
        private ShopUserRefRepository shopUserRefRepository;
        @Mock
        private DeviceRepository deviceRepository;

        @BeforeEach
        void setUp() {
                // Setup common mocks
        }

        @Test
        @DisplayName("测试门店完整生命周期")
        void testCompleteShopLifecycle() {
                // 测试门店的完整生命周期：创建 -> 绑定设备 -> 绑定用户 -> 删除

                // 1. 创建门店
                StoreAddVo addVo = new StoreAddVo();
                addVo.setStoreName("测试门店");
                addVo.setParentId(0L);
                addVo.setCUserId(1L);

                Shop savedShop = new Shop();
                savedShop.setId(1L);
                savedShop.setShopName("测试门店");
                savedShop.setParentId(0L);
                savedShop.setType(StoreTypeEnum.PARENT.getCode());
                savedShop.setStatus(StoreReviewEnum.PASS.ordinal());
                savedShop.setCreateUserId(1L);

                ShopUserRef savedRef = new ShopUserRef();
                savedRef.setId(1L);
                savedRef.setShopId(1L);
                savedRef.setUserId(1L);
                savedRef.setRole(StoreUserTypeEnum.ADMIN.getCode());

                when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                                .thenReturn(Collections.emptyList()); // 用户无绑定关系
                when(shopRepository.save(any(Shop.class))).thenReturn(savedShop);
                when(shopUserRefRepository.save(any(ShopUserRef.class))).thenReturn(savedRef);

                ResultBean<String> createResult = storeService.add(addVo);
                assertTrue(createResult.isOk());
                assertEquals("新增成功", createResult.getMsg());

                // 2. 绑定设备
                StoreBindDeviceParams bindDeviceParams = new StoreBindDeviceParams();
                bindDeviceParams.setStoreId(1L);
                bindDeviceParams.setDeviceIds(Arrays.asList(1L, 2L));
                bindDeviceParams.setUserId(1L);

                Device device1 = new Device();
                device1.setId(1L);
                device1.setShopId(null);

                Device device2 = new Device();
                device2.setId(2L);
                device2.setShopId(null);

                when(deviceRepository.findAllById(Arrays.asList(1L, 2L)))
                                .thenReturn(Arrays.asList(device1, device2));
                when(shopUserRefRepository.findByShopIdAndRole(1L, StoreUserTypeEnum.ADMIN.getCode()))
                                .thenReturn(savedRef);
                when(deviceRepository.saveAll(anyList())).thenReturn(Arrays.asList(device1, device2));

                ResultBean<String> bindDeviceResult = storeService.bindDevice(bindDeviceParams);
                assertTrue(bindDeviceResult.isOk());
                assertEquals("绑定设备成功", bindDeviceResult.getResultData());

                // 3. 绑定用户
                StoreBindUserParams bindUserParams = new StoreBindUserParams();
                bindUserParams.setStoreId(1L);
                bindUserParams.setUserId(2L);

                when(shopUserRefRepository.queryByUserId(2L))
                                .thenReturn(Collections.emptyList()); // 用户无绑定关系
                when(shopUserRefRepository.save(any(ShopUserRef.class)))
                                .thenReturn(savedRef);

                ResultBean<String> bindUserResult = storeService.bindUser(bindUserParams, 1L);
                assertTrue(bindUserResult.isOk());
                assertEquals("绑定用户成功", bindUserResult.getMsg());

                // 4. 删除门店
                when(shopRepository.getOne(1L)).thenReturn(savedShop);
                when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(1L)))
                                .thenReturn(Collections.emptyList()); // 无子店铺

                ResultBean<String> deleteResult = storeService.delete(1L);
                assertTrue(deleteResult.isOk());
                assertEquals("删除成功", deleteResult.getMsg());

                // 验证所有操作都被调用
                verify(shopRepository).save(any(Shop.class));
                verify(shopUserRefRepository, times(2)).save(any(ShopUserRef.class));
                verify(deviceRepository).saveAll(anyList());
                verify(shopRepository).deleteById(1L);
                verify(jdbcTemplate).update("delete from dub_user_shop_ref where shop_id = ?", 1L);
                verify(jdbcTemplate).update("delete from dub_device where shop_id = ?", 1L);
        }

        @Test
        @DisplayName("测试总店与分店关系")
        void testParentAndBranchShopRelationship() {
                // 测试总店和分店的关系管理

                // 1. 创建总店
                StoreAddVo parentAddVo = new StoreAddVo();
                parentAddVo.setStoreName("总店");
                parentAddVo.setParentId(0L);
                parentAddVo.setCUserId(1L);

                Shop parentShop = new Shop();
                parentShop.setId(1L);
                parentShop.setShopName("总店");
                parentShop.setParentId(0L);
                parentShop.setType(StoreTypeEnum.PARENT.getCode());
                parentShop.setStatus(StoreReviewEnum.PASS.ordinal());

                when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                                .thenReturn(Collections.emptyList());
                when(shopRepository.save(any(Shop.class))).thenReturn(parentShop);
                when(shopUserRefRepository.save(any(ShopUserRef.class)))
                                .thenReturn(new ShopUserRef());

                ResultBean<String> parentResult = storeService.add(parentAddVo);
                assertTrue(parentResult.isOk());

                // 2. 创建分店
                StoreAddVo branchAddVo = new StoreAddVo();
                branchAddVo.setStoreName("分店");
                branchAddVo.setParentId(1L);
                branchAddVo.setCUserId(2L);

                Shop branchShop = new Shop();
                branchShop.setId(2L);
                branchShop.setShopName("分店");
                branchShop.setParentId(1L);
                branchShop.setType(StoreTypeEnum.BRANCH.getCode());
                branchShop.setStatus(StoreReviewEnum.WAIT.ordinal());

                when(shopRepository.existsById(1L)).thenReturn(true);
                when(shopRepository.save(any(Shop.class))).thenReturn(branchShop);

                ResultBean<String> branchResult = storeService.add(branchAddVo);
                assertTrue(branchResult.isOk());

                // 3. 尝试删除有分店的总店（应该失败）
                when(shopRepository.getOne(1L)).thenReturn(parentShop);
                when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(1L)))
                                .thenReturn(Arrays.asList(branchShop)); // 有子店铺

                ResultBean<String> deleteParentResult = storeService.delete(1L);
                assertFalse(deleteParentResult.isOk());
                assertEquals("该店铺有子店铺，不能删除", deleteParentResult.getMsg());

                // 4. 先删除分店，再删除总店
                when(shopRepository.getOne(2L)).thenReturn(branchShop);
                when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(2L)))
                                .thenReturn(Collections.emptyList()); // 分店无子店铺

                ResultBean<String> deleteBranchResult = storeService.delete(2L);
                assertTrue(deleteBranchResult.isOk());

                when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(1L)))
                                .thenReturn(Collections.emptyList()); // 总店现在无子店铺

                ResultBean<String> deleteParentFinalResult = storeService.delete(1L);
                assertTrue(deleteParentFinalResult.isOk());
        }

        @Test
        @DisplayName("测试批量设备绑定")
        void testBatchDeviceBinding() {
                // 测试批量设备绑定
                StoreBindDeviceParams bindParams = new StoreBindDeviceParams();
                bindParams.setStoreId(1L);
                bindParams.setDeviceIds(Arrays.asList(1L, 2L, 3L, 4L, 5L)); // 5个设备
                bindParams.setUserId(1L);

                List<Device> devices = new ArrayList<>();
                for (int i = 1; i <= 5; i++) {
                        Device device = new Device();
                        device.setId((long) i);
                        device.setShopId(null);
                        devices.add(device);
                }

                ShopUserRef adminRef = new ShopUserRef();
                adminRef.setUserId(1L);

                when(deviceRepository.findAllById(Arrays.asList(1L, 2L, 3L, 4L, 5L)))
                                .thenReturn(devices);
                when(shopUserRefRepository.findByShopIdAndRole(1L, StoreUserTypeEnum.ADMIN.getCode()))
                                .thenReturn(adminRef);
                when(deviceRepository.saveAll(anyList())).thenReturn(devices);

                ResultBean<String> result = storeService.bindDevice(bindParams);

                assertTrue(result.isOk());
                verify(deviceRepository).saveAll(argThat(deviceList -> ((List<?>) deviceList).size() == 5 &&
                                ((List<Device>) deviceList).stream().allMatch(device -> device.getShopId().equals(1L) &&
                                                device.getUserId().equals(1L) &&
                                                device.getBindStatus().equals(1))));
        }

        @Test
        @DisplayName("测试复杂分页查询场景")
        void testComplexPageQuery() {
                // 测试复杂的分页查询
                StorePageParams pageParams = new StorePageParams();
                pageParams.setCurrentPage(2);
                pageParams.setPageSize(20);
                pageParams.setKeyword("测试");
                pageParams.setStoreType(StoreTypeEnum.PARENT.getCode());
                pageParams.setStoreId(1L);

                when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                                .thenReturn(100L); // 总共100条记录

                List<StorePageVo> storePageVos = new ArrayList<>();
                for (int i = 1; i <= 20; i++) {
                        StorePageVo vo = new StorePageVo();
                        vo.setId((long) i);
                        vo.setStoreName("测试门店" + i);
                        vo.setStoreType(String.valueOf(StoreTypeEnum.PARENT.getCode()));
                        vo.setMerchantCount(i);
                        vo.setDeviceCount(i * 2);
                        storePageVos.add(vo);
                }

                when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                                .thenReturn(storePageVos);

                ResultBean<PageResult<StorePageVo>> result = storeService.pageList(pageParams);

                assertTrue(result.isOk());
                PageResult<StorePageVo> pageResult = result.getResultData();
                assertNotNull(pageResult);
                assertEquals(100L, pageResult.getTotal());
                assertEquals(20, pageResult.getResult().size());

                // 验证查询条件
                ArgumentCaptor<String> sqlCaptor = ArgumentCaptor.forClass(String.class);
                verify(jdbcTemplate).queryForObject(sqlCaptor.capture(), eq(Long.class), anyVararg());
                String executedSql = sqlCaptor.getValue();
                assertTrue(executedSql.contains("shop.shop_name like"));
                assertTrue(executedSql.contains("shop.type ="));
        }

        @Test
        @DisplayName("测试错误恢复场景")
        void testErrorRecoveryScenarios() {
                // 测试错误恢复场景
                StoreAddVo addVo = new StoreAddVo();
                addVo.setStoreName("测试门店");
                addVo.setParentId(0L);
                addVo.setCUserId(1L);

                // 模拟数据库保存失败
                when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                                .thenReturn(Collections.emptyList());
                when(shopRepository.save(any(Shop.class)))
                                .thenThrow(new RuntimeException("Database error"));

                assertThrows(RuntimeException.class, () -> storeService.add(addVo));

                // 验证没有进行后续操作
                verify(shopUserRefRepository, never()).save(any(ShopUserRef.class));
        }
}
